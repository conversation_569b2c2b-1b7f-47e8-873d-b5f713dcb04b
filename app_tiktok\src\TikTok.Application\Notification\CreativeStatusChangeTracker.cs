using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using TikTok.Application.Contracts.Notification;
using TikTok.Enums;
using Volo.Abp.DependencyInjection;

namespace TikTok.Application.Notification
{
    /// <summary>
    /// Service để track và aggregate các thay đổi trạng thái creative
    /// </summary>
    public class CreativeStatusChangeTracker : ITransientDependency
    {
        private readonly ILogger<CreativeStatusChangeTracker> _logger;
        private readonly Dictionary<string, List<CreativeStatusChangeInfo>> _campaignChanges;

        public CreativeStatusChangeTracker(ILogger<CreativeStatusChangeTracker> logger)
        {
            _logger = logger;
            _campaignChanges = new Dictionary<string, List<CreativeStatusChangeInfo>>();
        }

        /// <summary>
        /// Track một thay đổi trạng thái creative
        /// </summary>
        public void TrackStatusChange(CreativeStatusChangeInfo changeInfo)
        {
            if (changeInfo == null || string.IsNullOrEmpty(changeInfo.CampaignId))
            {
                _logger.LogWarning("Invalid change info provided for tracking");
                return;
            }

            // Chỉ track các trạng thái có vấn đề
            if (!IsProblematicStatus(changeInfo.NewStatus))
            {
                _logger.LogDebug("Skipping non-problematic status change for creative {CreativeId}", changeInfo.CreativeId);
                return;
            }

            if (!_campaignChanges.ContainsKey(changeInfo.CampaignId))
            {
                _campaignChanges[changeInfo.CampaignId] = new List<CreativeStatusChangeInfo>();
            }

            _campaignChanges[changeInfo.CampaignId].Add(changeInfo);
            
            _logger.LogDebug("Tracked status change for campaign {CampaignId}, creative {CreativeId}: {OldStatus} -> {NewStatus}",
                changeInfo.CampaignId, changeInfo.CreativeId, changeInfo.OldStatus, changeInfo.NewStatus);
        }

        /// <summary>
        /// Lấy danh sách campaign summaries với các thay đổi đã được aggregate
        /// </summary>
        public List<CampaignStatusSummary> GetCampaignSummaries()
        {
            var summaries = new List<CampaignStatusSummary>();

            foreach (var campaignChange in _campaignChanges)
            {
                var campaignId = campaignChange.Key;
                var changes = campaignChange.Value;

                if (!changes.Any())
                    continue;

                var summary = new CampaignStatusSummary
                {
                    CampaignId = campaignId,
                    CampaignName = changes.First().CampaignName,
                    AdvertiserId = changes.First().AdvertiserId,
                    BcId = changes.First().BcId,
                    ChangedCreativeIds = changes.Select(c => c.CreativeId).Distinct().ToList(),
                    LastChangeTime = changes.Max(c => c.ChangeTime)
                };

                // Aggregate status counts
                foreach (var change in changes)
                {
                    if (change.NewStatus.HasValue)
                    {
                        summary.StatusCounts[change.NewStatus.Value] = 
                            summary.StatusCounts.GetValueOrDefault(change.NewStatus.Value, 0) + 1;
                    }
                }

                // Set total creatives (approximate - sẽ được cập nhật chính xác từ database)
                summary.TotalCreatives = summary.ChangedCreativeIds.Count;

                summaries.Add(summary);
            }

            _logger.LogInformation("Generated {Count} campaign summaries from tracked changes", summaries.Count);
            return summaries;
        }

        /// <summary>
        /// Xóa tất cả tracked changes
        /// </summary>
        public void Clear()
        {
            var totalChanges = _campaignChanges.Values.Sum(list => list.Count);
            _campaignChanges.Clear();
            _logger.LogDebug("Cleared {Count} tracked status changes", totalChanges);
        }

        /// <summary>
        /// Lấy số lượng campaigns đang được track
        /// </summary>
        public int GetTrackedCampaignsCount()
        {
            return _campaignChanges.Count;
        }

        /// <summary>
        /// Lấy tổng số thay đổi đang được track
        /// </summary>
        public int GetTotalChangesCount()
        {
            return _campaignChanges.Values.Sum(list => list.Count);
        }

        /// <summary>
        /// Kiểm tra xem trạng thái có phải là problematic không
        /// </summary>
        private bool IsProblematicStatus(CreativeDeliveryStatus? status)
        {
            if (!status.HasValue)
                return false;

            var problematicStatuses = new[]
            {
                CreativeDeliveryStatus.NOT_DELIVERYIN,
                CreativeDeliveryStatus.EXCLUDED,
                CreativeDeliveryStatus.UNAVAILABLE,
                CreativeDeliveryStatus.REJECTED
            };

            return problematicStatuses.Contains(status.Value);
        }
    }
}
