using System.Threading.Tasks;
using TikTok.Web.Views;
using Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared.Toolbars;

namespace TikTok.Web.CustomTopbar;

public class CustomTopbarToolbarContributor : IToolbarContributor
{
    public Task ConfigureToolbarAsync(IToolbarConfigurationContext context)
    {
        if (context.Toolbar.Name == StandardToolbars.Main)
        {
            // Add notification bell (left of currency)
            context.Toolbar.Items.Add(
                new ToolbarItem(typeof(NotificationBellViewComponent))
                {
                    Order = -1000
                }
            );
            
            // Add currency dropdown (right of notification bell)
            context.Toolbar.Items.Add(
                new ToolbarItem(typeof(CustomTopbarViewComponent))
                {
                    Order = -999
                }
            );
        }
        
        return Task.CompletedTask;
    }
}