using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TikTok.DbMigrator.Consts;
using TikTok.Entities;
using Volo.Abp.Data;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Guids;

namespace TikTok.DbMigrator.Data
{
    /// <summary>
    /// Data seeder cho Report Integrated AdAccount
    /// Tạo dữ liệu mẫu báo cáo tích hợp theo giờ cho tất cả Ad Accounts
    /// </summary>
    public class ReportIntegratedAdAccountDataSeeder : BaseRawDataSeeder
    {
        private readonly IRepository<RawReportIntegratedAdAccountEntity, Guid> _reportIntegratedAdAccountRepository;
        private readonly IGuidGenerator _guidGenerator;

        public ReportIntegratedAdAccountDataSeeder(
            IRepository<RawReportIntegratedAdAccountEntity, Guid> reportIntegratedAdAccountRepository,
            IGuidGenerator guidGenerator)
        {
            _reportIntegratedAdAccountRepository = reportIntegratedAdAccountRepository;
            _guidGenerator = guidGenerator;
        }

        public override async Task RunSeedAsync(DataSeedContext context)
        {
            // Kiểm tra xem đã có dữ liệu chưa
            if (await _reportIntegratedAdAccountRepository.GetCountAsync() > 0)
            {
                return; // Đã có dữ liệu, không cần tạo mới
            }

            // Tạo dữ liệu mẫu cho Report Integrated AdAccount
            await SeedSampleReportIntegratedAdAccountDataAsync();
        }

        private async Task SeedSampleReportIntegratedAdAccountDataAsync()
        {
            var allAdAccounts = DbMigratorConst.AdAccounts;
            var startDate = DateTime.UtcNow.AddDays(-30); // 30 ngày trước để có nhiều dữ liệu hơn
            var endDate = DateTime.UtcNow.Date; // Hôm nay
            var reports = new List<RawReportIntegratedAdAccountEntity>();

            // Tạo dữ liệu cho từng Ad Account theo từng giờ
            foreach (var adAccount in allAdAccounts)
            {
                // Tìm Business Center tương ứng
                var bc = DbMigratorConst.GetBusinessCenter(adAccount.BcId);
                if (bc == null) continue;

                for (var date = startDate.Date; date <= endDate; date = date.AddDays(1))
                {
                    for (int hour = 0; hour < 24; hour++)
                    {
                        var dateTime = date.AddHours(hour);

                        // Tạo dữ liệu ngẫu nhiên dựa trên AdAccount và thời gian
                        var seed = adAccount.AdvertiserId.GetHashCode() + dateTime.GetHashCode();
                        var adRandom = new Random(seed);

                        // Tạo dữ liệu hiệu suất dựa trên giờ trong ngày
                        var hourlyMultiplier = GetHourlyPerformanceMultiplier(hour);

                        // Hiệu suất thấp hơn vào cuối tuần
                        var isWeekend = date.DayOfWeek == DayOfWeek.Saturday || date.DayOfWeek == DayOfWeek.Sunday;
                        var weekendMultiplier = isWeekend ? 0.6 : 1.0;

                        // Hiệu suất tăng dần theo thời gian (trend tăng trưởng)
                        var daysSinceStart = (date - startDate.Date).Days;
                        var trendMultiplier = 1.0 + (daysSinceStart * 0.01); // Tăng 1% mỗi ngày

                        var totalMultiplier = hourlyMultiplier * weekendMultiplier * trendMultiplier;

                        // Tạo dữ liệu chi tiêu và hiệu suất
                        var baseSpend = adRandom.Next(100, 2000) * totalMultiplier;
                        var spend = Math.Max(0, baseSpend + (adRandom.NextDouble() - 0.5) * 200); // Có thể có biến động âm
                        var impressions = (long)Math.Max(0, adRandom.Next(2000, 50000) * totalMultiplier);
                        var clicks = (long)Math.Max(0, adRandom.Next(20, 1000) * totalMultiplier);

                        // Đảm bảo tỷ lệ hợp lý giữa clicks và impressions
                        if (impressions > 0 && clicks > impressions * 0.15)
                            clicks = (long)(impressions * (0.01 + adRandom.NextDouble() * 0.12)); // CTR 1-13%

                        // Tính reach và frequency hợp lý
                        var reach = impressions > 0 ?
                            Math.Min(impressions, (long)(impressions * (0.65 + adRandom.NextDouble() * 0.30))) : 0; // 65-95% của impressions
                        var frequency = impressions > 0 && reach > 0 ? (double)impressions / reach : 1.0;

                        // Đảm bảo frequency không quá cao (max 10)
                        if (frequency > 10.0)
                        {
                            frequency = 1.0 + adRandom.NextDouble() * 9.0; // 1-10
                            reach = impressions > 0 ? (long)(impressions / frequency) : 0;
                        }

                        var report = new RawReportIntegratedAdAccountEntity(_guidGenerator.Create())
                        {
                            AdvertiserId = adAccount.AdvertiserId,
                            AdvertiserName = adAccount.AdvertiserName,
                            Date = dateTime,
                            Spend = (decimal)spend,
                            BilledCost = spend > 0 ? (decimal)(spend * (0.90 + adRandom.NextDouble() * 0.08)) : 0, // 90-98% của spend
                            Impressions = impressions,
                            Clicks = clicks,
                            Ctr = impressions > 0 ? (decimal)((double)clicks / impressions * 100) : 0, // CTR as percentage
                            Cpm = impressions > 0 ? (decimal)(spend / impressions * 1000) : 0,
                            Cpc = clicks > 0 ? (decimal)(spend / clicks) : 0,
                            Reach = reach,
                            Frequency = (decimal)frequency,
                            Currency = bc.Currency, // Sử dụng currency từ Business Center
                            BcId = bc.BcId
                        };

                        reports.Add(report);
                    }
                }
            }

            // Bulk insert để tăng hiệu suất
            await _reportIntegratedAdAccountRepository.InsertManyAsync(reports);
        }

        /// <summary>
        /// Tính hệ số hiệu suất theo giờ trong ngày (dựa trên hành vi người dùng thực tế)
        /// </summary>
        /// <param name="hour">Giờ trong ngày (0-23)</param>
        /// <returns>Hệ số nhân cho hiệu suất</returns>
        private double GetHourlyPerformanceMultiplier(int hour)
        {
            // Hiệu suất cao nhất từ 7-9h sáng và 19-22h tối
            // Hiệu suất thấp nhất từ 0-5h sáng
            // Dựa trên nghiên cứu hành vi người dùng TikTok
            return hour switch
            {
                >= 0 and <= 5 => 0.15,  // Đêm khuya - hiệu suất rất thấp
                6 => 0.4,               // Sáng sớm - bắt đầu tăng
                7 => 0.8,               // Sáng - tăng mạnh
                8 => 1.2,               // Giờ cao điểm sáng
                9 => 1.1,               // Vẫn cao
                >= 10 and <= 11 => 0.9, // Buổi sáng - giảm nhẹ
                12 => 1.0,              // Giờ nghỉ trưa - ổn định
                13 => 1.1,              // Sau nghỉ trưa - tăng nhẹ
                >= 14 and <= 16 => 0.8, // Chiều - giảm
                17 => 0.9,              // Cuối giờ làm - tăng nhẹ
                18 => 1.1,              // Sau giờ làm - tăng
                19 => 1.3,              // Giờ vàng tối - cao nhất
                20 => 1.4,              // Peak time - cao nhất
                21 => 1.3,              // Vẫn rất cao
                22 => 1.1,              // Giảm nhẹ
                23 => 0.7,              // Đêm - giảm mạnh
                _ => 1.0                // Default
            };
        }
    }
}