using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using TikTok.AdAccounts;
using TikTok.BusinessCenters;
using TikTok.Entities;
using TikTok.Permissions;
using TikTok.Repositories;
using TikTok.ResourcePermissions;
using TikTok.Users;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;
using Volo.Abp.ObjectMapping;

namespace TikTok.UserAccessManagement
{
    /// <summary>
    /// Batch data container for user information to avoid N+1 queries
    /// </summary>
    public class BatchUserData
    {
        public Dictionary<Guid, IdentityUser> UserEntities { get; set; } = new Dictionary<Guid, IdentityUser>();
        public Dictionary<Guid, List<string>> UserRoles { get; set; } = new Dictionary<Guid, List<string>>();
        public Dictionary<Guid, int> AdAccountCounts { get; set; } = new Dictionary<Guid, int>();
        public Dictionary<Guid, List<string>> AdAccountIds { get; set; } = new Dictionary<Guid, List<string>>();
    }

    /// <summary>
    /// Application Service cho User Access Management
    /// </summary>
    [Authorize(TikTokPermissions.AdAccountPermissionManagement.Default)]
    [RemoteService(IsEnabled = false)]
    public class UserAccessManagementAppService : ApplicationService, IUserAccessManagementAppService
    {
        private readonly IUserManagementService _userManagementService;
        private readonly IAdAccountAppService _adAccountAppService;
        private readonly IAdAccountDapperRepository _adAccountDapperRepository;
        private readonly IResourcePermissionAppService _resourcePermissionAppService;
        private readonly IIdentityUserRepository _identityUserRepository;
        private readonly IdentityUserManager _userManager;
        private readonly IBusinessCenterRepository _businessCenterRepository;
        private readonly ILogger<UserAccessManagementAppService> _logger;

        public UserAccessManagementAppService(
            IUserManagementService userManagementService,
            IAdAccountAppService adAccountAppService,
            IAdAccountDapperRepository adAccountDapperRepository,
            IResourcePermissionAppService resourcePermissionAppService,
            IIdentityUserRepository identityUserRepository,
            IdentityUserManager userManager,
            IBusinessCenterRepository businessCenterRepository,
            ILogger<UserAccessManagementAppService> logger)
        {
            _userManagementService = userManagementService;
            _adAccountAppService = adAccountAppService;
            _adAccountDapperRepository = adAccountDapperRepository;
            _resourcePermissionAppService = resourcePermissionAppService;
            _identityUserRepository = identityUserRepository;
            _userManager = userManager;
            _businessCenterRepository = businessCenterRepository;
            _logger = logger;
        }

        /// <summary>
        /// Lấy tên BC theo BC ID
        /// </summary>
        private async Task<string> GetBcNameAsync(string bcId)
        {
            try
            {
                var bc = await _businessCenterRepository.GetByBcIdAsync(bcId);
                return bc?.Name ?? $"BC {bcId}";
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to get BC name for ID: {BcId}, using fallback", bcId);
                return $"BC {bcId}";
            }
        }

        /// <summary>
        /// Lấy danh sách người dùng với thông tin truy cập
        /// </summary>
        [Authorize(TikTokPermissions.AdAccountPermissionManagement.Default)]
        public async Task<PagedResultDto<UserAccessDto>> GetUsersAsync(GetUserAccessListDto input)
        {
            try
            {
                _logger.LogDebug("Getting users list with access information. Filter: {Filter}", input.Filter);

                // Lấy danh sách người dùng từ UserManagementService
                var userInput = new GetUserListDto
                {
                    Filter = input.Filter,
                    UserName = input.UserName,
                    Email = input.Email,
                    Name = input.Name,
                    MaxResultCount = input.MaxResultCount,
                    SkipCount = input.SkipCount,
                    Sorting = input.Sorting
                };

                var usersResult = await _userManagementService.GetUserListAsync(userInput);
                var userAccessList = new List<UserAccessDto>();

                // Batch load all data to avoid N+1 queries
                var userIds = usersResult.Items.Select(u => u.Id).ToList();
                var batchData = await LoadBatchDataForUsers(userIds);

                // Process users with batch loaded data
                foreach (var user in usersResult.Items)
                {
                    var userAccess = await MapToUserAccessDtoWithBatchData(user, batchData);
                    
                    // Áp dụng các bộ lọc bổ sung
                    if (ShouldIncludeUser(userAccess, input))
                    {
                        userAccessList.Add(userAccess);
                    }
                }

                // Áp dụng lọc và sắp xếp
                var filteredUsers = ApplyAdditionalFilters(userAccessList, input);
                var totalCount = filteredUsers.Count;

                // Phân trang
                var pagedUsers = filteredUsers
                    .Skip(input.SkipCount)
                    .Take(input.MaxResultCount)
                    .ToList();

                _logger.LogDebug("Retrieved {Count} users with access information", pagedUsers.Count);

                return new PagedResultDto<UserAccessDto>
                {
                    TotalCount = totalCount,
                    Items = pagedUsers
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting users list");
                throw;
            }
        }

        /// <summary>
        /// Lấy thông tin truy cập của một người dùng cụ thể
        /// </summary>
        [Authorize(TikTokPermissions.AdAccountPermissionManagement.Default)]
        public async Task<UserAccessDto> GetUserAccessAsync(Guid userId)
        {
            try
            {
                _logger.LogDebug("Getting user access information for user: {UserId}", userId);

                var user = await _userManagementService.GetUserAsync(userId);
                if (user == null)
                {
                    throw new Volo.Abp.UserFriendlyException($"User with ID {userId} not found");
                }

                var userAccess = await MapToUserAccessDto(user);
                
                _logger.LogDebug("Retrieved access information for user: {UserName}", user.UserName);
                return userAccess;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting user access information for user: {UserId}", userId);
                throw;
            }
        }

        /// <summary>
        /// Batch load all required data for users to avoid N+1 queries
        /// </summary>
        private async Task<BatchUserData> LoadBatchDataForUsers(List<Guid> userIds)
        {
            try
            {
                var batchData = new BatchUserData();

                if (!userIds.Any())
                    return batchData;

                // Batch load user entities
                var userEntities = new List<IdentityUser>();
                foreach (var userId in userIds)
                {
                    try
                    {
                        var userEntity = await _identityUserRepository.GetAsync(userId);
                        userEntities.Add(userEntity);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Could not retrieve user entity: {UserId}", userId);
                    }
                }
                batchData.UserEntities = userEntities.ToDictionary(u => u.Id, u => u);

                // Batch load roles for all users
                var userRoles = new Dictionary<Guid, List<string>>();
                foreach (var userEntity in userEntities)
                {
                    try
                    {
                        var roles = await _userManager.GetRolesAsync(userEntity);
                        userRoles[userEntity.Id] = roles.ToList();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Could not retrieve roles for user: {UserId}", userEntity.Id);
                        userRoles[userEntity.Id] = new List<string>();
                    }
                }
                batchData.UserRoles = userRoles;

                // Batch load ad account counts with single query
                var adAccountCounts = new Dictionary<Guid, int>();
                var adAccountIds = new Dictionary<Guid, List<string>>();

                try
                {
                    // Single query to get all permissions for all users
                    var allUserPermissions = await _resourcePermissionAppService.GetAllAsync(new GetResourcePermissionListDto
                    {
                        UserIds = userIds,
                        ResourceType = "AdAccount",
                        MaxResultCount = 10000
                    });
                    
                    var adAccountPermissions = allUserPermissions
                        .GroupBy(p => p.UserId)
                        .ToDictionary(g => g.Key, g => g.Select(p => p.ResourceId).ToList());

                    // Set counts and IDs for each user
                    foreach (var userId in userIds)
                    {
                        if (adAccountPermissions.ContainsKey(userId))
                        {
                            var userAdvertiserIds = adAccountPermissions[userId];
                            adAccountCounts[userId] = userAdvertiserIds.Count;
                            adAccountIds[userId] = userAdvertiserIds.ToList();
                        }
                        else
                        {
                            adAccountCounts[userId] = 0;
                            adAccountIds[userId] = new List<string>();
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error batch loading ad account data for users");
                    // Set default values for all users
                    foreach (var userId in userIds)
                    {
                        adAccountCounts[userId] = 0;
                        adAccountIds[userId] = new List<string>();
                    }
                }

                batchData.AdAccountCounts = adAccountCounts;
                batchData.AdAccountIds = adAccountIds;

                return batchData;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error batch loading data for users");
                return new BatchUserData(); // Return empty batch data on error
            }
        }

        /// <summary>
        /// Map UserDto to UserAccessDto using batch loaded data
        /// </summary>
        private Task<UserAccessDto> MapToUserAccessDtoWithBatchData(UserDto user, BatchUserData batchData)
        {
            var userAccess = ObjectMapper.Map<UserDto, UserAccessDto>(user);

            // Set basic properties
            userAccess.IsActive = user.IsActive;

            // Set ad account data from batch
            if (batchData.AdAccountCounts.ContainsKey(user.Id))
            {
                userAccess.AdAccountsCount = batchData.AdAccountCounts[user.Id];
                userAccess.AssignedAdAccountIds = batchData.AdAccountIds[user.Id];
            }
            else
            {
                userAccess.AdAccountsCount = 0;
                userAccess.AssignedAdAccountIds = new List<string>();
            }

            // Set role data from batch
            if (batchData.UserRoles.ContainsKey(user.Id))
            {
                var roles = batchData.UserRoles[user.Id];
                userAccess.RoleName = roles.FirstOrDefault() ?? "No Role";
                userAccess.RoleNames = roles;
            }
            else
            {
                userAccess.RoleName = "No Role";
                userAccess.RoleNames = new List<string>();
            }

            return Task.FromResult(userAccess);
        }

        /// <summary>
        /// Optimized mapping to UserAccessDto with minimal database calls (legacy method)
        /// </summary>
        private async Task<UserAccessDto> MapToUserAccessDtoOptimized(UserDto user)
        {
            var userAccess = ObjectMapper.Map<UserDto, UserAccessDto>(user);

            // Set basic properties
            userAccess.IsActive = user.IsActive;

            // Get ad account count with optimized query
            try
            {
                var userPermissionsPaged = await _resourcePermissionAppService.GetByUserIdAsync(user.Id);
                var adAccountPermissions = userPermissionsPaged.Items
                    .Where(p => p.ResourceType == "AdAccount")
                    .Select(p => p.ResourceId)
                    .ToList();
                
                userAccess.AdAccountsCount = adAccountPermissions.Count;
                userAccess.AssignedAdAccountIds = adAccountPermissions;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Could not retrieve ad account access for user: {UserId}", user.Id);
                userAccess.AdAccountsCount = 0;
                userAccess.AssignedAdAccountIds = new List<string>();
            }

            // Get role information
            try
            {
                var roles = await _userManager.GetRolesAsync(await _identityUserRepository.GetAsync(user.Id));
                userAccess.RoleName = roles.FirstOrDefault() ?? "No Role";
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Could not retrieve roles for user: {UserId}", user.Id);
                userAccess.RoleName = "No Role";
            }

            return userAccess;
        }





        /// <summary>
        /// Lấy danh sách tài khoản quảng cáo có thể phân quyền được group theo BC (sử dụng Dapper để bypass giới hạn)
        /// </summary>
        [Authorize(TikTokPermissions.AdAccountPermissionManagement.Default)]
        public async Task<List<BcAdAccountGroupDto>> GetAvailableAdAccountsGroupedByBcAsync(GetAvailableAdAccountsDto input)
        {
            try
            {
                _logger.LogDebug("Getting available ad accounts grouped by BC for user: {UserId}", input.UserId);

                // Lấy permissions trực tiếp
                var userPermissionsPaged = await _resourcePermissionAppService.GetByUserIdAsync(input.UserId);
                var assignedAdvertiserIds = userPermissionsPaged.Items
                    .Where(p => p.ResourceType == "AdAccount")
                    .Select(p => p.ResourceId)
                    .ToHashSet();

                // Sử dụng Dapper để bypass giới hạn 1000 của EF Core
                _logger.LogInformation("Fetching ad accounts via Dapper for user: {UserId}, Filter: {Filter}, BcId: {BcId}, AdvertiserId: {AdvertiserId}", 
                    input.UserId, input.Filter, input.BcId, input.AdvertiserId);
                
                var allAdAccountEntities = await _adAccountDapperRepository.GetAllAdAccountsAsync(
                    input.Filter, 
                    input.BcId, 
                    input.AdvertiserId
                );

                _logger.LogInformation("Retrieved {Count} ad account entities via Dapper", allAdAccountEntities.Count);

                // Map entities to DTOs
                var allAdAccounts = ObjectMapper.Map<List<RawAdAccountEntity>, List<AdAccountDto>>(allAdAccountEntities);

                // Lọc ra những tài khoản chưa được phân quyền
                var availableAdAccounts = allAdAccounts
                    .Where(a => !assignedAdvertiserIds.Contains(a.AdvertiserId))
                    .ToList();

                // Group theo BC
                var bcGroups = new List<BcAdAccountGroupDto>();
                var groupedAccounts = availableAdAccounts.GroupBy(a => a.OwnerBcId ?? "Unknown");
                
                foreach (var group in groupedAccounts)
                {
                    var bcName = await GetBcNameAsync(group.Key);
                    bcGroups.Add(new BcAdAccountGroupDto
                    {
                        BcId = group.Key,
                        BcName = bcName, // Just BC name without ID
                        AdAccounts = group.ToList(),
                        IsExpanded = false
                    });
                }
                
                bcGroups = bcGroups.OrderBy(g => g.BcName).ToList();

                _logger.LogDebug("Found {Count} BC groups with {TotalAccounts} available ad accounts for user: {UserId} (unlimited via Dapper)", 
                    bcGroups.Count, availableAdAccounts.Count, input.UserId);

                return bcGroups;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting available ad accounts grouped by BC for user: {UserId}", input.UserId);
                throw;
            }
        }

        /// <summary>
        /// Lấy danh sách tài khoản quảng cáo đã phân quyền được group theo BC
        /// </summary>
        [Authorize(TikTokPermissions.AdAccountPermissionManagement.Default)]
        public async Task<List<BcAdAccountGroupDto>> GetAssignedAdAccountsGroupedByBcAsync(GetAssignedAdAccountsDto input)
        {
            try
            {
                _logger.LogDebug("Getting assigned ad accounts grouped by BC for user: {UserId}", input.UserId);

                // Lấy danh sách quyền của user đối với tài khoản quảng cáo
                var userPermissionsPaged = await _resourcePermissionAppService.GetByUserIdAsync(input.UserId);
                var adAccountPermissions = userPermissionsPaged.Items
                    .Where(p => p.ResourceType == "AdAccount")
                    .Select(p => p.ResourceId)
                    .Distinct()
                    .ToList();

                if (!adAccountPermissions.Any())
                {
                    return new List<BcAdAccountGroupDto>();
                }

                // Sử dụng Dapper để bypass giới hạn 1000 của EF Core
                _logger.LogInformation("Fetching assigned ad accounts via Dapper for user: {UserId}, Filter: {Filter}, BcId: {BcId}, AdvertiserId: {AdvertiserId}", 
                    input.UserId, input.Filter, input.BcId, input.AdvertiserId);
                
                var allAdAccountEntities = await _adAccountDapperRepository.GetAllAdAccountsAsync(
                    input.Filter, 
                    input.BcId, 
                    input.AdvertiserId
                );

                _logger.LogInformation("Retrieved {Count} ad account entities via Dapper for assigned accounts", allAdAccountEntities.Count);

                // Map entities to DTOs
                var allAdAccountsResult = ObjectMapper.Map<List<RawAdAccountEntity>, List<AdAccountDto>>(allAdAccountEntities);
                
                // Filter để chỉ lấy những ad accounts được assign cho user này
                var assignedAdAccounts = allAdAccountsResult
                    .Where(a => adAccountPermissions.Contains(a.AdvertiserId))
                    .ToList();

                // Group theo BC
                var bcGroups = new List<BcAdAccountGroupDto>();
                var groupedAccounts = assignedAdAccounts.GroupBy(a => a.OwnerBcId ?? "Unknown");
                
                foreach (var group in groupedAccounts)
                {
                    var bcName = await GetBcNameAsync(group.Key);
                    bcGroups.Add(new BcAdAccountGroupDto
                    {
                        BcId = group.Key,
                        BcName = bcName, // Just BC name without ID
                        AdAccounts = group.ToList(),
                        IsExpanded = false
                    });
                }
                
                bcGroups = bcGroups.OrderBy(g => g.BcName).ToList();

                _logger.LogDebug("Found {Count} BC groups with {TotalAccounts} assigned ad accounts for user: {UserId} (unlimited via Dapper)", 
                    bcGroups.Count, assignedAdAccounts.Count, input.UserId);

                return bcGroups;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting assigned ad accounts grouped by BC for user: {UserId}", input.UserId);
                throw;
            }
        }


        /// <summary>
        /// Phân quyền tài khoản quảng cáo cho người dùng
        /// </summary>
        [Authorize(TikTokPermissions.AdAccountPermissionManagement.Default)]
        public async Task AssignAdAccountsAsync(AssignAdAccountsDto input)
        {
            try
            {
                _logger.LogDebug("Assigning {Count} ad accounts to user: {UserId}", input.AdAccountIds.Count, input.UserId);

                // Kiểm tra user tồn tại
                var user = await _userManagementService.GetUserAsync(input.UserId);
                if (user == null)
                {
                    throw new Volo.Abp.UserFriendlyException($"User with ID {input.UserId} not found");
                }

                // Tạo danh sách quyền cần phân cho từng tài khoản quảng cáo
                var permissionsToAssign = new List<ResourcePermissions.AssignPermissionForResourceDto>();

                foreach (var adAccountId in input.AdAccountIds)
                {
                    permissionsToAssign.Add(new ResourcePermissions.AssignPermissionForResourceDto
                    {
                        UserId = input.UserId,
                        ResourceId = adAccountId,
                        ResourceType = "AdAccount",
                        Permissions = new List<string> { "View", "Access" } // Quyền cơ bản
                    });
                }

                // Thực hiện phân quyền
                await _resourcePermissionAppService.AssignPermissionsAsync(permissionsToAssign);

                _logger.LogInformation("Successfully assigned {Count} ad accounts to user: {UserId}", input.AdAccountIds.Count, input.UserId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while assigning ad accounts to user: {UserId}", input.UserId);
                throw;
            }
        }

        /// <summary>
        /// Thu hồi quyền truy cập tài khoản quảng cáo của người dùng
        /// </summary>
        [Authorize(TikTokPermissions.AdAccountPermissionManagement.Default)]
        public async Task RevokeAdAccountsAsync(RevokeAdAccountsDto input)
        {
            try
            {
                _logger.LogDebug("Revoking {Count} ad accounts from user: {UserId}", input.AdAccountIds.Count, input.UserId);

                // Kiểm tra user tồn tại
                var user = await _userManagementService.GetUserAsync(input.UserId);
                if (user == null)
                {
                    throw new Volo.Abp.UserFriendlyException($"User with ID {input.UserId} not found");
                }

                // Thu hồi quyền cho từng tài khoản quảng cáo
                foreach (var adAccountId in input.AdAccountIds)
                {
                    await _resourcePermissionAppService.RevokePermissionsAsync(input.UserId, adAccountId, "AdAccount");
                }

                _logger.LogInformation("Successfully revoked {Count} ad accounts from user: {UserId}", input.AdAccountIds.Count, input.UserId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while revoking ad accounts from user: {UserId}", input.UserId);
                throw;
            }
        }

        /// <summary>
        /// Lấy thống kê tổng quan về quyền truy cập
        /// </summary>
        [Authorize(TikTokPermissions.AdAccountPermissionManagement.Default)]
        public async Task<UserAccessStatisticsDto> GetStatisticsAsync()
        {
            try
            {
                _logger.LogDebug("Getting user access statistics");

                var statistics = new UserAccessStatisticsDto();

                // Lấy thống kê người dùng
                var allUsers = await _userManagementService.GetAllUsersAsync();
                statistics.TotalUsers = allUsers.Count;
                statistics.ActiveUsers = allUsers.Count(u => u.IsActive);

                // OPTIMIZED: Lấy count thông qua GetListAsync với MaxResultCount = 1
                var adAccountsResult = await _adAccountAppService.GetListAsync(new GetAdAccountListDto { MaxResultCount = 1 });
                statistics.TotalAdAccounts = (int)adAccountsResult.TotalCount;

                // OPTIMIZED: Batch load permissions để tránh N+1 query
                var userIds = allUsers.Select(u => u.Id).ToList();
                var allUserPermissions = await _resourcePermissionAppService.GetAllAsync(new GetResourcePermissionListDto
                {
                    UserIds = userIds,
                    ResourceType = "AdAccount",
                    MaxResultCount = 10000
                });

                var userPermissionsMap = allUserPermissions
                    .GroupBy(p => p.UserId)
                    .ToDictionary(g => g.Key, g => g.ToList());

                // Đếm số người dùng có quyền truy cập tài khoản quảng cáo
                var usersWithAccess = 0;
                var assignedAdAccountsSet = new HashSet<string>();

                foreach (var user in allUsers)
                {
                    if (userPermissionsMap.ContainsKey(user.Id))
                    {
                        var adAccountPermissions = userPermissionsMap[user.Id];
                    if (adAccountPermissions.Any())
                    {
                        usersWithAccess++;
                        foreach (var permission in adAccountPermissions)
                        {
                            assignedAdAccountsSet.Add(permission.ResourceId);
                            }
                        }
                    }
                }

                statistics.UsersWithAdAccountAccess = usersWithAccess;
                statistics.AssignedAdAccounts = assignedAdAccountsSet.Count;
                statistics.UnassignedAdAccounts = statistics.TotalAdAccounts - statistics.AssignedAdAccounts;

                // Thống kê theo vai trò
                var usersByRole = new Dictionary<string, int>(StringComparer.OrdinalIgnoreCase);
                foreach (var user in allUsers)
                {
                    try
                    {
                        var identityUser = await _identityUserRepository.FindAsync(user.Id);
                        if (identityUser != null)
                        {
                            var roles = await _userManager.GetRolesAsync(identityUser);
                            var primaryRole = roles.FirstOrDefault() ?? "No Role";
                            if (!usersByRole.ContainsKey(primaryRole))
                            {
                                usersByRole[primaryRole] = 0;
                            }
                            usersByRole[primaryRole]++;
                        }
                        else
                        {
                            if (!usersByRole.ContainsKey("No Role"))
                            {
                                usersByRole["No Role"] = 0;
                            }
                            usersByRole["No Role"]++;
                        }
                    }
                    catch
                    {
                        if (!usersByRole.ContainsKey("Unknown"))
                        {
                            usersByRole["Unknown"] = 0;
                        }
                        usersByRole["Unknown"]++;
                    }
                }
                statistics.UsersByRole = usersByRole;

                _logger.LogDebug("Retrieved user access statistics");
                return statistics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting user access statistics");
                throw;
            }
        }

        #region Private Helper Methods

        /// <summary>
        /// Chuyển đổi UserDto thành UserAccessDto
        /// </summary>
        private async Task<UserAccessDto> MapToUserAccessDto(UserDto user)
        {
            var userAccess = ObjectMapper.Map<UserDto, UserAccessDto>(user);
            userAccess.UserId = user.Id;

            // Lấy thông tin quyền truy cập tài khoản quảng cáo
            try
            {
                var userPermissionsPaged = await _resourcePermissionAppService.GetByUserIdAsync(user.Id);
                var adAccountPermissions = userPermissionsPaged.Items
                    .Where(p => p.ResourceType == "AdAccount")
                    .Select(p => p.ResourceId)
                    .ToList();
                
                userAccess.AdAccountsCount = adAccountPermissions.Count;
                userAccess.AssignedAdAccountIds = adAccountPermissions;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Could not retrieve ad account access for user: {UserId}", user.Id);
                userAccess.AdAccountsCount = 0;
                userAccess.AssignedAdAccountIds = new List<string>();
            }

            // Lấy thông tin vai trò
            try
            {
                var identityUser = await _identityUserRepository.FindAsync(user.Id);
                if (identityUser != null)
                {
                    var roles = await _userManager.GetRolesAsync(identityUser);
                    userAccess.RoleNames = roles.ToList();
                    userAccess.RoleName = roles.FirstOrDefault() ?? "No Role";
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Could not retrieve roles for user: {UserId}", user.Id);
                userAccess.RoleName = "Unknown";
                userAccess.RoleNames = new List<string>();
            }

            return userAccess;
        }

        /// <summary>
        /// Kiểm tra xem có nên bao gồm user trong kết quả hay không
        /// </summary>
        private bool ShouldIncludeUser(UserAccessDto userAccess, GetUserAccessListDto input)
        {
            // Lọc theo vai trò
            if (!string.IsNullOrEmpty(input.RoleName) &&
                !userAccess.RoleNames.Any(r => r.Contains(input.RoleName, StringComparison.OrdinalIgnoreCase)))
            {
                return false;
            }

            // Lọc theo trạng thái hoạt động
            if (input.IsActive.HasValue && userAccess.IsActive != input.IsActive.Value)
            {
                return false;
            }

            // Lọc theo việc có quyền truy cập tài khoản quảng cáo
            if (input.HasAdAccountAccess.HasValue)
            {
                var hasAccess = userAccess.AdAccountsCount > 0;
                if (hasAccess != input.HasAdAccountAccess.Value)
                {
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// Áp dụng các bộ lọc bổ sung và sắp xếp
        /// </summary>
        private List<UserAccessDto> ApplyAdditionalFilters(List<UserAccessDto> users, GetUserAccessListDto input)
        {
            var query = users.AsQueryable();

            // Text search across common fields (UserName, Email, Name)
            if (!string.IsNullOrWhiteSpace(input.Filter))
            {
                var keyword = input.Filter.Trim();
                query = query.Where(u =>
                    (!string.IsNullOrEmpty(u.UserName) && u.UserName.Contains(keyword, StringComparison.OrdinalIgnoreCase)) ||
                    (!string.IsNullOrEmpty(u.Email) && u.Email.Contains(keyword, StringComparison.OrdinalIgnoreCase)) ||
                    (!string.IsNullOrEmpty(u.Name) && u.Name.Contains(keyword, StringComparison.OrdinalIgnoreCase))
                );
            }

            // Specific field filters (contains for flexibility)
            if (!string.IsNullOrWhiteSpace(input.UserName))
            {
                var userNameKey = input.UserName.Trim();
                query = query.Where(u => !string.IsNullOrEmpty(u.UserName) && u.UserName.Contains(userNameKey, StringComparison.OrdinalIgnoreCase));
            }
            if (!string.IsNullOrWhiteSpace(input.Email))
            {
                var emailKey = input.Email.Trim();
                query = query.Where(u => !string.IsNullOrEmpty(u.Email) && u.Email.Contains(emailKey, StringComparison.OrdinalIgnoreCase));
            }
            if (!string.IsNullOrWhiteSpace(input.Name))
            {
                var nameKey = input.Name.Trim();
                query = query.Where(u => !string.IsNullOrEmpty(u.Name) && u.Name.Contains(nameKey, StringComparison.OrdinalIgnoreCase));
            }

            // Sắp xếp
            if (!string.IsNullOrEmpty(input.Sorting))
            {
                // Implement sorting logic based on input.Sorting
                if (input.Sorting.Contains("userName"))
                {
                    query = input.Sorting.Contains("desc")
                        ? query.OrderByDescending(u => u.UserName)
                        : query.OrderBy(u => u.UserName);
                }
                else if (input.Sorting.Contains("email"))
                {
                    query = input.Sorting.Contains("desc")
                        ? query.OrderByDescending(u => u.Email)
                        : query.OrderBy(u => u.Email);
                }
                else if (input.Sorting.Contains("adAccountsCount"))
                {
                    query = input.Sorting.Contains("desc")
                        ? query.OrderByDescending(u => u.AdAccountsCount)
                        : query.OrderBy(u => u.AdAccountsCount);
                }
                else
                {
                    query = query.OrderBy(u => u.UserName);
                }
            }
            else
            {
                query = query.OrderBy(u => u.UserName);
            }

            return query.ToList();
        }

        #endregion
    }
}
