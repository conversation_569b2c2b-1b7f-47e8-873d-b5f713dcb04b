namespace TikTok.Consts
{
    /// <summary>
    /// Notification templates following Meeting Management pattern
    /// Similar to NotificationTemplateTitleConst
    /// </summary>
    public static class TikTokNotificationTemplateConst
    {
        // Template placeholders
        public const string CampaignName = "<--CampaignName-->";
        public const string DisabledCount = "<--DisabledCount-->";
        public const string TotalCount = "<--TotalCount-->";
        public const string Timestamp = "<--Timestamp-->";
        public const string NotDeliveryCount = "<--NotDeliveryCount-->";
        public const string ExcludedCount = "<--ExcludedCount-->";
        public const string UnavailableCount = "<--UnavailableCount-->";
        public const string RejectedCount = "<--RejectedCount-->";
        public const string TotalProblematicCount = "<--TotalProblematicCount-->";
        
        /// <summary>
        /// Templates for GMV Max Creative Status Change notifications
        /// </summary>
        public static class GmvMaxCreativeStatusChange
        {
            public const string CAMPAIGN_MANAGER = 
                "[GMV Max Alert] Campaign <--CampaignName--> có <--TotalProblematicCount--> video đang gặp vấn đề: " +
                "<--NotDeliveryCount--> không được phân phối, <--ExcludedCount--> bị loại trừ, " +
                "<--UnavailableCount--> không khả dụng, <--RejectedCount--> bị từ chối";
                
            public const string CREATIVE_APPROVER = 
                "[Creative Review] Campaign <--CampaignName--> cần kiểm tra <--TotalProblematicCount--> video có vấn đề: " +
                "<--NotDeliveryCount--> không được phân phối, <--ExcludedCount--> bị loại trừ, " +
                "<--UnavailableCount--> không khả dụng, <--RejectedCount--> bị từ chối";
                
            public const string ADVERTISER = 
                "[Thông báo] Chiến dịch <--CampaignName--> của bạn có <--TotalProblematicCount--> video đang gặp vấn đề: " +
                "<--NotDeliveryCount--> không được phân phối, <--ExcludedCount--> bị loại trừ, " +
                "<--UnavailableCount--> không khả dụng, <--RejectedCount--> bị từ chối";
                
            public const string ACCOUNT_MANAGER = 
                "[Theo dõi] Campaign <--CampaignName--> có <--TotalProblematicCount--> video cần chú ý: " +
                "<--NotDeliveryCount--> không được phân phối, <--ExcludedCount--> bị loại trừ, " +
                "<--UnavailableCount--> không khả dụng, <--RejectedCount--> bị từ chối";
        }
    }
}
