using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Entities;
using TikTok.Enums;
using TikTok.Repositories;
using TikTok.Application.Contracts.Notification;
using TikTok.Application.Notification;
using TikTok.Consts;
using Volo.Abp;

namespace TikTok.Application.Notification
{
    /// <summary>
    /// Service chung để test tất cả loại notification system
    /// Hiện tại hỗ trợ GMV Max Creative Status Change
    /// Có thể mở rộng cho các loại notification khác trong tương lai
    /// </summary>
    public class NotificationTestService : INotificationTestService
    {
        private readonly IRawGmvMaxProductCreativeReportRepository _gmvMaxProductCreativeReportRepository;
        private readonly IAssetRepository _assetRepository;
        private readonly IRawGmvMaxCampaignsRepository _gmvMaxCampaignsRepository;
        private readonly ITikTokNotificationService _tikTokNotificationService;
        private readonly CreativeStatusChangeTracker _statusChangeTracker;
        private readonly ILogger<NotificationTestService> _logger;

        public NotificationTestService(
            IRawGmvMaxProductCreativeReportRepository gmvMaxProductCreativeReportRepository,
            ILogger<NotificationTestService> logger,
            IAssetRepository assetRepository,
            IRawGmvMaxCampaignsRepository gmvMaxCampaignsRepository,
            ITikTokNotificationService tikTokNotificationService,
            CreativeStatusChangeTracker statusChangeTracker)
        {
            _gmvMaxProductCreativeReportRepository = gmvMaxProductCreativeReportRepository;
            _assetRepository = assetRepository;
            _gmvMaxCampaignsRepository = gmvMaxCampaignsRepository;
            _tikTokNotificationService = tikTokNotificationService;
            _statusChangeTracker = statusChangeTracker;
            _logger = logger;
        }

        /// <summary>
        /// Test notification với data thật nhưng giới hạn phạm vi
        /// </summary>
        public async Task<NotificationTestResult> TestNotificationAsync(
            string campaignId, 
            string notificationType = "GmvMaxCreativeStatusChange",
            int maxDays = 1)
        {
            var result = new NotificationTestResult
            {
                NotificationType = notificationType,
                CampaignId = campaignId,
                StartTime = DateTime.UtcNow
            };

            try
            {
                _logger.LogInformation("=== BẮT ĐẦU TEST NOTIFICATION ===");
                _logger.LogInformation("Type: {NotificationType}, Campaign: {CampaignId}, MaxDays: {MaxDays}", 
                    notificationType, campaignId, maxDays);

                // Hiện tại chỉ hỗ trợ GMV Max Creative Status Change
                // Có thể mở rộng cho các loại notification khác
                switch (notificationType.ToLower())
                {
                    case "gmvmaxcreativestatuschange":
                    case "gmvmax":
                        await TestGmvMaxCreativeStatusChangeAsync(campaignId, maxDays, result);
                        break;
                    default:
                        throw new BusinessException($"Notification type '{notificationType}' is not supported yet");
                }

                result.Success = true;
                _logger.LogInformation("=== HOÀN THÀNH TEST NOTIFICATION ===");
            }
            catch (BusinessException ex)
            {
                result.Success = false;
                result.ErrorMessage = ex.Message;
                result.ErrorCode = ex.Code ?? string.Empty;
                _logger.LogError(ex, "Lỗi khi Test Notification cho Campaign: {CampaignId}, Type: {NotificationType}", campaignId, notificationType);
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = $"Lỗi khi Test Notification: {ex.Message}";
                _logger.LogError(ex, "Lỗi khi Test Notification cho Campaign: {CampaignId}, Type: {NotificationType}", campaignId, notificationType);
            }
            finally
            {
                result.EndTime = DateTime.UtcNow;
                result.Duration = result.EndTime - result.StartTime;
            }

            return result;
        }

        /// <summary>
        /// Test GMV Max Creative Status Change notification - CÁCH ĐƠN GIẢN
        /// </summary>
        private async Task TestGmvMaxCreativeStatusChangeAsync(
            string campaignId, 
            int maxDays, 
            NotificationTestResult result)
        {
            _logger.LogInformation("TEST: Bắt đầu test notification - CÁCH ĐƠN GIẢN");

            try
            {
                // 1. Kiểm tra campaign có tồn tại không
                var campaign = await _gmvMaxCampaignsRepository.FindAsync(c => c.CampaignId == campaignId);
                if (campaign == null)
                {
                    _logger.LogWarning("TEST: Campaign không tồn tại: {CampaignId}", campaignId);
                    result.ErrorMessage = $"Campaign {campaignId} không tồn tại";
                    return;
                }

                _logger.LogInformation("TEST: Tìm thấy campaign: {CampaignId} - {CampaignName}", campaign.CampaignId, campaign.CampaignName);

                // 2. Lấy tất cả product creative của campaign này trong maxDays ngày gần nhất
                var endDate = DateTime.UtcNow;
                var startDate = endDate.AddDays(-maxDays);
                
                var existingRecords = await _gmvMaxProductCreativeReportRepository.GetListAsync(
                    x => x.CampaignId == campaignId && 
                         x.Date >= startDate && 
                         x.Date <= endDate);

                if (!existingRecords.Any())
                {
                    _logger.LogWarning("TEST: Không tìm thấy product creative nào cho campaign {CampaignId} trong {MaxDays} ngày gần nhất", 
                        campaignId, maxDays);
                    result.ErrorMessage = $"Không tìm thấy product creative nào cho campaign {campaignId} trong {maxDays} ngày gần nhất";
                    return;
                }

                _logger.LogInformation("TEST: Tìm thấy {Count} product creative để test", existingRecords.Count);

                // 3. Đổi trạng thái tất cả sang problematic
                var problematicStatuses = new[] 
                { 
                    CreativeDeliveryStatus.NOT_DELIVERYIN, 
                    CreativeDeliveryStatus.EXCLUDED, 
                    CreativeDeliveryStatus.UNAVAILABLE, 
                    CreativeDeliveryStatus.REJECTED 
                };

                var newRecords = new List<RawGmvMaxProductCreativeReportEntity>();
                var statusChanges = new List<CreativeStatusChangeInfo>();

                foreach (var existingRecord in existingRecords)
                {
                    var newStatus = problematicStatuses[new Random().Next(problematicStatuses.Length)];
                    var oldStatus = existingRecord.CreativeDeliveryStatus;

                    _logger.LogInformation("TEST: Đổi trạng thái {CreativeId} từ {OldStatus} sang {NewStatus}", 
                        existingRecord.ItemId, oldStatus, newStatus);

                    // Tạo bản ghi mới với trạng thái problematic
                    var newRecord = new RawGmvMaxProductCreativeReportEntity(Guid.NewGuid())
                    {
                        BcId = existingRecord.BcId,
                        AdvertiserId = existingRecord.AdvertiserId,
                        StoreId = existingRecord.StoreId,
                        CampaignId = existingRecord.CampaignId,
                        ItemGroupId = existingRecord.ItemGroupId,
                        ItemId = existingRecord.ItemId, // Cùng ItemId để trigger status change
                        Date = DateTime.UtcNow, // Ngày mới hơn
                        CreativeDeliveryStatus = newStatus, // Trạng thái problematic
                        Title = existingRecord.Title,
                        TtAccountName = existingRecord.TtAccountName,
                        Currency = existingRecord.Currency,
                        CreativeType = existingRecord.CreativeType,
                        Orders = existingRecord.Orders,
                        GrossRevenue = existingRecord.GrossRevenue,
                        ProductImpressions = existingRecord.ProductImpressions,
                        ProductClicks = existingRecord.ProductClicks
                    };

                    newRecords.Add(newRecord);

                    // Track status change
                    statusChanges.Add(new CreativeStatusChangeInfo
                    {
                        CampaignId = existingRecord.CampaignId,
                        CreativeId = existingRecord.ItemId,
                        OldStatus = oldStatus,
                        NewStatus = newStatus,
                        AdvertiserId = existingRecord.AdvertiserId,
                        BcId = existingRecord.BcId,
                        ChangeTime = DateTime.UtcNow,
                        CampaignName = campaign.CampaignName
                    });
                }

                // 4. Insert tất cả bản ghi mới - OPTIMIZED với batch size
                const int BATCH_SIZE = 1000;
                if (newRecords.Count > BATCH_SIZE)
                {
                    _logger.LogInformation("TEST: Inserting {Count} records in batches of {BatchSize}", newRecords.Count, BATCH_SIZE);
                    for (int i = 0; i < newRecords.Count; i += BATCH_SIZE)
                    {
                        var batch = newRecords.Skip(i).Take(BATCH_SIZE);
                        await _gmvMaxProductCreativeReportRepository.InsertManyAsync(batch);
                        _logger.LogInformation("TEST: Inserted batch {BatchNumber}/{TotalBatches}", (i / BATCH_SIZE) + 1, (newRecords.Count + BATCH_SIZE - 1) / BATCH_SIZE);
                    }
                }
                else
                {
                    await _gmvMaxProductCreativeReportRepository.InsertManyAsync(newRecords);
                }
                _logger.LogInformation("TEST: Đã tạo {Count} bản ghi mới với trạng thái problematic", newRecords.Count);

                // 5. Track tất cả status changes
                foreach (var statusChange in statusChanges)
                {
                    _statusChangeTracker.TrackStatusChange(statusChange);
                }

                _logger.LogInformation("TEST: Đã track {Count} status changes", statusChanges.Count);

                // 6. Gửi notification
                await ProcessStatusChangeNotificationsAsync(result);

                result.TotalRecords = existingRecords.Count;
                result.NewRecords = newRecords.Count;
                result.CampaignsProcessed = 1;
                result.CreativesProcessed = existingRecords.Count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "TEST: Lỗi khi test notification");
                throw;
            }

            _logger.LogInformation("TEST: Kết quả - Tổng: {Total}, Mới: {New}, Notifications: {Notifications}", 
                result.TotalRecords, result.NewRecords, result.NotificationsSent);
        }



        private async Task<string> GetCampaignNameAsync(string campaignId)
        {
            try
            {
                var campaign = await _gmvMaxCampaignsRepository.FindAsync(c => c.CampaignId == campaignId);
                return campaign?.CampaignName ?? $"Campaign {campaignId}";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "TEST: Error getting campaign name for {CampaignId}", campaignId);
                return $"Campaign {campaignId}";
            }
        }

        /// <summary>
        /// Kiểm tra xem trạng thái có phải là problematic không
        /// </summary>
        private bool IsProblematicStatus(CreativeDeliveryStatus? status)
        {
            if (!status.HasValue)
                return false;

            var problematicStatuses = new[]
            {
                CreativeDeliveryStatus.NOT_DELIVERYIN,
                CreativeDeliveryStatus.EXCLUDED,
                CreativeDeliveryStatus.UNAVAILABLE,
                CreativeDeliveryStatus.REJECTED
            };

            return problematicStatuses.Contains(status.Value);
        }

        /// <summary>
        /// Xử lý thông báo cho các thay đổi trạng thái creative
        /// </summary>
        private async Task ProcessStatusChangeNotificationsAsync(NotificationTestResult result)
        {
            try
            {
                var campaignSummaries = _statusChangeTracker.GetCampaignSummaries();
                
                if (!campaignSummaries.Any())
                {
                    _logger.LogInformation("TEST: No campaign summaries to process for notifications");
                    return;
                }

                _logger.LogInformation("TEST: Processing notifications for {Count} campaigns with status changes", campaignSummaries.Count);

                foreach (var summary in campaignSummaries)
                {
                    if (summary.HasProblematicStatuses)
                    {
                        _logger.LogInformation("TEST: Sending notification for campaign {CampaignId} with {ProblematicCount} problematic creatives",
                            summary.CampaignId, summary.ProblematicCreativesCount);
                        
                        // KIỂM TRA: CampaignId trước khi gửi notification
                        _logger.LogInformation("TEST: CampaignId being sent to notification: {CampaignId}", summary.CampaignId);

                        var success = await _tikTokNotificationService.SendCampaignNotificationAsync(
                            summary.CampaignId,
                            TikTokNotificationConst.GmvMaxCreativeStatusChange,
                            new Dictionary<string, object>
                            {
                                ["StatusSummary"] = summary,
                                ["ChangeTime"] = summary.LastChangeTime,
                                ["ProblematicCount"] = summary.ProblematicCreativesCount
                            });

                        if (success)
                        {
                            result.NotificationsSent++;
                            result.CampaignsWithNotifications.Add(summary.CampaignId);
                            _logger.LogInformation("TEST: Successfully sent notification for campaign {CampaignId}", summary.CampaignId);
                        }
                        else
                        {
                            _logger.LogWarning("TEST: Failed to send notification for campaign {CampaignId}", summary.CampaignId);
                        }
                    }
                    else
                    {
                        _logger.LogInformation("TEST: Skipping notification for campaign {CampaignId} - no problematic statuses", summary.CampaignId);
                    }
                }

                _statusChangeTracker.Clear();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "TEST: Error processing status change notifications");
            }
        }
    }
}
