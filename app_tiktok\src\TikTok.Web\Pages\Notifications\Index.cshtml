@page "/notifications"
@using Microsoft.Extensions.Localization
@using TikTok.Localization
@inject IStringLocalizer<TikTokResource> L

<div class="container py-3">
    <div class="d-flex align-items-center justify-content-between mb-3">
        <h5 class="mb-0">@L["AllNotifications"]</h5>
        <button type="button" class="btn btn-sm btn-outline-secondary" id="markAllRead" onclick="NotificationsPage.markAllRead()">@L["MarkAllAsRead"]</button>
    </div>

    <div id="notificationsContainer" class="list-group shadow-sm">
        <div class="text-center text-muted py-4">
            <i class="fa-solid fa-bell-slash fa-2x mb-2"></i>
            <p class="mb-0">@L["NoNewNotifications"]</p>
        </div>
    </div>
    <div id="lazySentinel" class="py-3 text-center text-muted" style="display:none;">@L["Loading"]...</div>
</div>

@section Styles {
    <link href="/css/notifications/index.css?v=@DateTime.Now.Ticks" rel="stylesheet" />
}

@section Scripts {
    <script>
        window.notificationsL10n = {
            NoNewNotifications: @Html.Raw(System.Text.Json.JsonSerializer.Serialize(L["NoNewNotifications"].Value)),
            Loading: @Html.Raw(System.Text.Json.JsonSerializer.Serialize(L["Loading"].Value)),
            JustNow: @Html.Raw(System.Text.Json.JsonSerializer.Serialize(L["JustNow"].Value)),
            MinutesAgo: @Html.Raw(System.Text.Json.JsonSerializer.Serialize(L["MinutesAgo"].Value)),
            HoursAgo: @Html.Raw(System.Text.Json.JsonSerializer.Serialize(L["HoursAgo"].Value)),
            DaysAgo: @Html.Raw(System.Text.Json.JsonSerializer.Serialize(L["DaysAgo"].Value))
        };
    </script>
    <script src="/js/notifications/index.js?v=@DateTime.Now.Ticks"></script>
}


