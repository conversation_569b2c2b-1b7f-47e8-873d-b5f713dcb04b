using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using TikTok.AdAccounts;
using Volo.Abp.Application.Dtos;

namespace TikTok.UserAccessManagement
{
    /// <summary>
    /// DTO cho việc phân quyền tài khoản quảng cáo cho người dùng
    /// </summary>
    public class AssignAdAccountsDto
    {
        /// <summary>
        /// ID người dùng
        /// </summary>
        [Required]
        public Guid UserId { get; set; }

        /// <summary>
        /// Danh sách ID tài khoản quảng cáo cần phân quyền
        /// </summary>
        [Required]
        public List<string> AdAccountIds { get; set; } = new List<string>();
    }

    /// <summary>
    /// DTO cho việc thu hồi quyền truy cập tài khoản quảng cáo
    /// </summary>
    public class RevokeAdAccountsDto
    {
        /// <summary>
        /// ID người dùng
        /// </summary>
        [Required]
        public Guid UserId { get; set; }

        /// <summary>
        /// Danh sách ID tài khoản quảng cáo cần thu hồi quyền
        /// </summary>
        [Required]
        public List<string> AdAccountIds { get; set; } = new List<string>();
    }

    /// <summary>
    /// DTO cho việc lấy danh sách tài khoản quảng cáo có thể phân quyền
    /// </summary>
    public class GetAvailableAdAccountsDto
    {
        /// <summary>
        /// ID người dùng
        /// </summary>
        public Guid UserId { get; set; }

        /// <summary>
        /// Từ khóa tìm kiếm
        /// </summary>
        public string? Filter { get; set; }

        /// <summary>
        /// Lọc theo Business Center ID
        /// </summary>
        public string? BcId { get; set; }

        /// <summary>
        /// Lọc theo Advertiser ID
        /// </summary>
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// Số lượng tối đa trả về
        /// </summary>
        public int MaxResultCount { get; set; } = 50;

        /// <summary>
        /// Số lượng bản ghi bỏ qua (cho pagination)
        /// </summary>
        public int SkipCount { get; set; } = 0;
    }


    /// <summary>
    /// DTO để lấy danh sách tài khoản quảng cáo đã được phân quyền với phân trang
    /// </summary>
    public class GetAssignedAdAccountsDto : PagedAndSortedResultRequestDto
    {
        /// <summary>
        /// ID của người dùng
        /// </summary>
        [Required]
        public Guid UserId { get; set; }

        /// <summary>
        /// Từ khóa tìm kiếm (tên, ID, BC)
        /// </summary>
        public string? Filter { get; set; }

        /// <summary>
        /// Lọc theo Business Center ID (optional)
        /// </summary>
        public string? BcId { get; set; }

        /// <summary>
        /// Lọc theo Advertiser ID (optional)
        /// </summary>
        public string? AdvertiserId { get; set; }
    }

    /// <summary>
    /// DTO cho BC group với danh sách ad accounts
    /// </summary>
    public class BcAdAccountGroupDto
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Business Center Name
        /// </summary>
        public string BcName { get; set; } = string.Empty;

        /// <summary>
        /// Danh sách ad accounts trong BC này
        /// </summary>
        public List<AdAccountDto> AdAccounts { get; set; } = new List<AdAccountDto>();

        /// <summary>
        /// Tổng số ad accounts trong BC
        /// </summary>
        public int TotalCount => AdAccounts.Count;

        /// <summary>
        /// BC có được expand không
        /// </summary>
        public bool IsExpanded { get; set; } = false;
    }
}
