using System;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Domain.Entities.Auditing;

namespace TikTok.Entities
{
    /// <summary>
    /// Fact table lưu trữ thông tin chi tiêu chi tiết của tài khoản quảng cáo theo thời gian
    /// </summary>
    public class FactDailySpendEntity : AuditedEntity<Guid>
    {
        /// <summary>
        /// Khóa ngoại liên kết với Dim_Date (YYYYMMDD)
        /// </summary>
        [Required]
        public int DimDateId { get; set; }

        /// <summary>
        /// Khóa ngoại liên kết với Dim_AdAccount
        /// </summary>
        [Required]
        public Guid DimAdAccountId { get; set; }

        /// <summary>
        /// Khóa ngoại liên kết với Dim_BusinessCenter
        /// </summary>
        [Required]
        public Guid DimBusinessCenterId { get; set; }

        /// <summary>
        /// ID nhà quảng cáo (Business Key)
        /// </summary>
        [Required]
        [StringLength(50)]
        public string AdvertiserId { get; set; }

        /// <summary>
        /// Tên nhà quảng cáo
        /// </summary>
        [Required]
        [StringLength(200)]
        public string AdvertiserName { get; set; }

        /// <summary>
        /// ID Business Center (Business Key)
        /// </summary>
        [Required]
        [StringLength(100)]
        public string BcId { get; set; }

        /// <summary>
        /// Tổng số tiền chi phí cho nhà quảng cáo
        /// </summary>
        [Required]
        public decimal TotalAmount { get; set; }
        public decimal? TotalAmountVND { get; set; }
        public decimal? TotalAmountUSD { get; set; }

        /// <summary>
        /// Số tiền chi phí tiền mặt
        /// </summary>
        [Required]
        public decimal CashAmount { get; set; }
        public decimal? CashAmountVND { get; set; }
        public decimal? CashAmountUSD { get; set; }

        /// <summary>
        /// Số tiền chi phí tín dụng quảng cáo
        /// </summary>
        [Required]
        public decimal GrantAmount { get; set; }
        public decimal? GrantAmountVND { get; set; }
        public decimal? GrantAmountUSD { get; set; }

        /// <summary>
        /// Số tiền thuế ước tính
        /// </summary>
        public decimal TaxAmount { get; set; }
        public decimal? TaxAmountVND { get; set; }
        public decimal? TaxAmountUSD { get; set; }
        
        /// <summary>
        /// Tiền tệ theo mã ISO 4217
        /// </summary>
        [Required]
        [StringLength(10)]
        public string Currency { get; set; }

        /// <summary>
        /// Ngày chi tiêu (UTC)
        /// </summary>
        [Required]
        public DateTime Date { get; set; }

        /// <summary>
        /// Navigation property cho DimDate
        /// </summary>
        public virtual DimDateEntity DimDate { get; set; }

        /// <summary>
        /// Navigation property cho DimAdAccount
        /// </summary>
        public virtual DimAdAccountEntity DimAdAccount { get; set; }

        /// <summary>
        /// Navigation property cho DimBusinessCenter
        /// </summary>
        public virtual DimBusinessCenterEntity DimBusinessCenter { get; set; }
    }
} 