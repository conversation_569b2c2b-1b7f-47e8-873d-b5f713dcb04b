using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using TikTok.AdAccounts;
using TikTok.Permissions;
using TikTok.UserAccessManagement;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.AspNetCore.Mvc;

namespace TikTok.Controllers
{
    /// <summary>
    /// Controller cho User Access Management
    /// </summary>
    [RemoteService(Name = "TikTok")]
    [Area("app")]
    [Route("api/app/user-access-management")]
    [Authorize(TikTokPermissions.AdAccountPermissionManagement.Default)]
    public class UserAccessManagementController : TikTokController
    {
        private readonly IUserAccessManagementAppService _userAccessManagementAppService;

        public UserAccessManagementController(IUserAccessManagementAppService userAccessManagementAppService)
        {
            _userAccessManagementAppService = userAccessManagementAppService;
        }

        /// <summary>
        /// Lấy danh sách người dùng với thông tin truy cập
        /// </summary>
        /// <param name="input">Thông tin lọc và phân trang</param>
        /// <returns>Danh sách người dùng</returns>
        [HttpGet]
        [Authorize(TikTokPermissions.AdAccountPermissionManagement.Default)]
        public async Task<PagedResultDto<UserAccessDto>> GetUsersAsync([FromQuery] GetUserAccessListDto input)
        {
            return await _userAccessManagementAppService.GetUsersAsync(input);
        }

        /// <summary>
        /// Lấy thông tin truy cập của một người dùng cụ thể
        /// </summary>
        /// <param name="userId">ID người dùng</param>
        /// <returns>Thông tin truy cập</returns>
        [HttpGet("{userId:guid}")]
        [Authorize(TikTokPermissions.AdAccountPermissionManagement.Default)]
        public async Task<UserAccessDto> GetUserAccessAsync(Guid userId)
        {
            return await _userAccessManagementAppService.GetUserAccessAsync(userId);
        }





        /// <summary>
        /// Lấy danh sách tài khoản quảng cáo có thể phân quyền được group theo BC
        /// </summary>
        /// <param name="input">Thông tin tìm kiếm</param>
        /// <returns>Danh sách BC groups với ad accounts</returns>
        [HttpGet("available-ad-accounts-grouped-by-bc")]
        [Authorize(TikTokPermissions.AdAccountPermissionManagement.Default)]
        public async Task<List<BcAdAccountGroupDto>> GetAvailableAdAccountsGroupedByBcAsync([FromQuery] GetAvailableAdAccountsDto input)
        {
            return await _userAccessManagementAppService.GetAvailableAdAccountsGroupedByBcAsync(input);
        }

        /// <summary>
        /// Lấy danh sách tài khoản quảng cáo đã phân quyền được group theo BC
        /// </summary>
        /// <param name="input">Thông tin tìm kiếm</param>
        /// <returns>Danh sách BC groups với ad accounts</returns>
        [HttpGet("assigned-ad-accounts-grouped-by-bc")]
        [Authorize(TikTokPermissions.AdAccountPermissionManagement.Default)]
        public async Task<List<BcAdAccountGroupDto>> GetAssignedAdAccountsGroupedByBcAsync([FromQuery] GetAssignedAdAccountsDto input)
        {
            return await _userAccessManagementAppService.GetAssignedAdAccountsGroupedByBcAsync(input);
        }


        /// <summary>
        /// Phân quyền tài khoản quảng cáo cho người dùng
        /// </summary>
        /// <param name="input">Thông tin phân quyền</param>
        /// <returns>Kết quả thực hiện</returns>
        [HttpPost("assign-ad-accounts")]
        [Authorize(TikTokPermissions.AdAccountPermissionManagement.Default)]
        public async Task AssignAdAccountsAsync([FromBody] AssignAdAccountsDto input)
        {
            await _userAccessManagementAppService.AssignAdAccountsAsync(input);
        }

        /// <summary>
        /// Thu hồi quyền truy cập tài khoản quảng cáo của người dùng
        /// </summary>
        /// <param name="input">Thông tin thu hồi quyền</param>
        /// <returns>Kết quả thực hiện</returns>
        [HttpPost("revoke-ad-accounts")]
        [Authorize(TikTokPermissions.AdAccountPermissionManagement.Default)]
        public async Task RevokeAdAccountsAsync([FromBody] RevokeAdAccountsDto input)
        {
            await _userAccessManagementAppService.RevokeAdAccountsAsync(input);
        }

        /// <summary>
        /// Lấy thống kê tổng quan về quyền truy cập
        /// </summary>
        /// <returns>Thông tin thống kê</returns>
        [HttpGet("statistics")]
        [Authorize(TikTokPermissions.AdAccountPermissionManagement.Default)]
        public async Task<UserAccessStatisticsDto> GetStatisticsAsync()
        {
            return await _userAccessManagementAppService.GetStatisticsAsync();
        }
    }
}
