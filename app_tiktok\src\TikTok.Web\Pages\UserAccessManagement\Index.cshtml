@page "~/UserAccessManagement"
@using TikTok.Localization
@using TikTok.Web.Pages.UserAccessManagement
@using TikTok.Permissions
@using Microsoft.AspNetCore.Authorization
@using Microsoft.Extensions.Localization
@model IndexModel
@inject IStringLocalizer<TikTokResource> L
@inject IAuthorizationService AuthorizationService
@{
    ViewData["Title"] = "User Access Management";
}

@section styles {
    <link rel="stylesheet" href="/Pages/UserAccessManagement/userAccess.css" />
}

@section scripts {
    <abp-script src="/Pages/UserAccessManagement/userAccessPermission.js" />
    <abp-script src="/Pages/UserAccessManagement/Index.js" />
}

<abp-card>
    <abp-card-header>
        <abp-row>
            <abp-column size-md="_6">
                <abp-card-title>@L["UserAccessManagement:Title"]</abp-card-title>
            </abp-column>
            <abp-column size-md="_6" class="text-end">
            </abp-column>
        </abp-row>
    </abp-card-header>
    <abp-card-body>
        <!-- Filter Controls -->
        <div class="row mb-3 row-gap-3 align-items-end">
            <div class="col-6 col-md-2 ms-auto text-end">
                <select class="form-select" id="filterRole">
                    <option value="">@L["AllRoles"]</option>
                    <option value="admin">Admin</option>
                    <option value="supporter">Supporter</option>
                    <option value="customer">Customer</option>
                </select>
            </div>
            <div class="col-6 col-md-4 text-end">
                <input type="text" class="form-control" id="filterUser" placeholder="@L["SearchByNameEmailUsername"]" />
            </div>
        </div>

        

        <!-- Users Table -->
        <abp-table striped-rows="true" id="UsersTable">
            <thead>
                <tr>
                    <th>@L["UserName"]</th>
                    <th>@L["Email"]</th>
                    <th>@L["Name"]</th>
                    <th>@L["Role"]</th>
                    <th class="d-none">@L["AdAccountsCount"]</th>
                    <th>@L["Status"]</th>
                    <th>@L["Actions"]</th>
                </tr>
            </thead>
            <tbody>
                <!-- Initial loading row -->
                <tr id="initial-loading-row">
                    <td colspan="7" class="text-center py-4">
                        <div class="d-flex justify-content-center align-items-center">
                            <div class="spinner-border text-primary me-3" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <span class="text-muted">@L["LoadingUsers"]...</span>
                        </div>
                    </td>
                </tr>
            </tbody>
        </abp-table>
    </abp-card-body>
</abp-card>

<!-- Ad Account Access Management Modal (XL like NotificationRules) -->
<div class="modal fade" id="AdAccountAccessModal" tabindex="-1" role="dialog" aria-labelledby="AdAccountAccessModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-xxl" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title fs-5 text-white" id="AdAccountAccessModalLabel">@L["ManageAdAccountAccess"].Value</h5>
                <button type="button" class="btn text-white" data-bs-dismiss="modal" aria-label="Close">
                    <i class="bi bi-x-lg"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="row mt-3">
                    @if (await AuthorizationService.IsGrantedAsync(TikTokPermissions.AdAccountPermissionManagement.Default))
                    {
                        <div class="col-md-6">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <h6 class="mb-0">@L["AvailableAdAccounts"]</h6>
                                <button type="button" id="btnAddAll" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-plus-square"></i> @L["AddAll"]
                                </button>
                            </div>
                            <div class="mb-2">
                                <div class="input-group input-group-sm">
                                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                                    <input type="text" id="AvailableAdAccountsSearch" class="form-control" placeholder="@L["SearchByNameIdBc"]" />
                                    <button class="btn btn-outline-secondary" type="button" id="ClearAvailableSearch" title="@L["Clear"]" data-bs-toggle="tooltip" data-bs-placement="top">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                            <div id="AvailableAdAccountsList" class="list-group uam-list">
                                <!-- Available ad accounts grouped by BC will be loaded here -->
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <h6 class="mb-0">@L["AssignedAdAccounts"]</h6>
                                <button type="button" id="btnRemoveAll" class="btn btn-outline-danger btn-sm" >
                                    <i class="fas fa-minus-square"></i> @L["RemoveAll"]
                                </button>
                            </div>
                            <div class="mb-2">
                                <div class="input-group input-group-sm">
                                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                                    <input type="text" id="AssignedAdAccountsSearch" class="form-control" placeholder="@L["SearchByNameIdBc"]" />
                                    <button class="btn btn-outline-secondary" type="button" id="ClearAssignedSearch" title="@L["Clear"]" data-bs-toggle="tooltip" data-bs-placement="top">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                            <div id="AssignedAdAccountsList" class="list-group uam-list">
                                <!-- Assigned ad accounts grouped by BC will be loaded here -->
                            </div>
                        </div>
                    }
                    else
                    {
                        <div class="col">
                            <h6>@L["AssignedAdAccounts"]</h6>
                            <div class="mb-2">
                                <div class="input-group input-group-sm">
                                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                                    <input type="text" id="AssignedAdAccountsSearchReadOnly" class="form-control" placeholder="@L["SearchByNameIdBc"]" />
                                </div>
                            </div>
                            <div id="AssignedAdAccountsListReadOnly" class="list-group uam-list">
                                <!-- Assigned ad accounts will be loaded here (read-only) -->
                            </div>
                        </div>
                    }
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">@L["Close"]</button>
                @if (await AuthorizationService.IsGrantedAsync(TikTokPermissions.AdAccountPermissionManagement.Default))
                {
                    <button type="button" class="btn btn-primary" id="SaveAdAccountAccess">@L["Save"]</button>
                }
            </div>
        </div>
    </div>
</div>
