using Microsoft.Extensions.Logging;
using Module.Notifications;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Application.Contracts.Notification;
using TikTok.BaseMessages;
using Volo.Abp.DependencyInjection;

namespace TikTok.Application.Notification
{
    /// <summary>
    /// Main service for TikTok notification operations
    /// Simplified approach without GenericFactory dependency
    /// </summary>
    public class TikTokNotificationService : ITikTokNotificationService, ITransientDependency
    {
        private readonly IEnumerable<ITikTokNotificationBuildProvider> _notificationBuilders;
        private readonly Module.Notifications.INotificationService _notificationService;
        private readonly ILogger<TikTokNotificationService> _logger;

        public TikTokNotificationService(
            IEnumerable<ITikTokNotificationBuildProvider> notificationBuilders,
            Module.Notifications.INotificationService notificationService,
            ILogger<TikTokNotificationService> logger)
        {
            _notificationBuilders = notificationBuilders;
            _notificationService = notificationService;
            _logger = logger;
        }

        public async Task<bool> SendCampaignNotificationAsync(
            string campaignId,
            string context,
            Dictionary<string, object> metadata = null)
        {
            try
            {
                _logger.LogInformation("Sending campaign notification: {CampaignId}, Context: {Context}",
                    campaignId, context);

                // Get appropriate notification builder using simple lookup
                var builder = await GetNotificationBuilderAsync(context);
                if (builder == null)
                {
                    _logger.LogWarning("No notification builder found for context: {Context}", context);
                    return false;
                }

                // Build notifications
                var notifications = await builder.BuildNotifications(campaignId);
                if (!notifications.Any())
                {
                    _logger.LogInformation("No notifications to send for campaign: {CampaignId}", campaignId);
                    // Important: indicate nothing was sent to callers
                    return false;
                }

                // Send notifications
                var results = new List<bool>();
                foreach (var notification in notifications)
                {
                    var result = await SendSingleNotification(notification);
                    results.Add(result);
                }

                var successCount = results.Count(r => r);
                _logger.LogInformation("Sent {SuccessCount}/{TotalCount} notifications for campaign: {CampaignId}",
                    successCount, results.Count, campaignId);

                return successCount > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending campaign notification: {CampaignId}", campaignId);
                return false;
            }
        }

        public async Task<bool> SendNotificationToUsersAsync(
            string objectId,
            string context,
            IEnumerable<TikTokNotificationUserDto> users)
        {
            try
            {
                var builder = await GetNotificationBuilderAsync(context);
                if (builder == null)
                {
                    return false;
                }

                var notifications = await builder.BuildNotificationByUsers(objectId, users);

                var results = new List<bool>();
                foreach (var notification in notifications)
                {
                    var result = await SendSingleNotification(notification);
                    results.Add(result);
                }

                return results.Any(r => r);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending notifications to users");
                return false;
            }
        }

        public async Task<ITikTokNotificationBuildProvider> GetNotificationBuilderAsync(string context)
        {
            try
            {
                // Simple lookup by context string
                var builder = _notificationBuilders.FirstOrDefault(b => b.Context.Equals(context, StringComparison.OrdinalIgnoreCase));
                return await Task.FromResult(builder);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error resolving notification builder for context: {Context}", context);
                return null;
            }
        }

        /// <summary>
        /// Send a single notification using the module notification service
        /// </summary>
        private async Task<bool> SendSingleNotification(TikTokNotificationDto notification)
        {
            try
            {
                _logger.LogDebug("Processing notification: UserId={UserId}, ObjectId={ObjectId}", 
                    notification.UserId, notification.ObjectId);

                // Validate UserId format
                if (string.IsNullOrEmpty(notification.UserId) || !Guid.TryParse(notification.UserId, out var userId))
                {
                    _logger.LogError("Invalid UserId format: {UserId}", notification.UserId);
                    return false;
                }

                // ObjectId can be string (like campaignId) or Guid
                // If it's a valid Guid, use it; otherwise generate a new one
                Guid objectId;
                if (string.IsNullOrEmpty(notification.ObjectId))
                {
                    objectId = Guid.NewGuid();
                }
                else if (Guid.TryParse(notification.ObjectId, out objectId))
                {
                    // ObjectId is a valid Guid, use it
                    _logger.LogDebug("Using ObjectId as Guid: {ObjectId}", objectId);
                }
                else
                {
                    // ObjectId is not a Guid (e.g., campaignId string), generate new Guid
                    objectId = Guid.NewGuid();
                    _logger.LogDebug("ObjectId is not Guid format (likely campaignId): {ObjectId}, generated new Guid: {NewGuid}", 
                        notification.ObjectId, objectId);
                }

                // Use INotificationService from module Notification
                var request = new Module.Notifications.Requests.SendNotificationReq
                {
                    UserId = userId,
                    Notifcation = new Module.Notifications.Requests.NotificationRequest
                    {
                        Title = notification.Title,
                        Content = notification.Content
                    },
                    Context = notification.Context ?? "GmvMaxCreativeStatusChange",
                    ObjectId = objectId,
                    PhoneNumber = notification.PhoneNumber,
                    IdempotentKey = Guid.NewGuid().ToString(),
                    Payload = notification.Payload ?? System.Text.Json.JsonSerializer.Serialize(new
                    {
                        AdAccountId = notification.AdAccountId,
                        BcId = notification.BcId,
                        UserType = notification.UserType.ToString(),
                        Metadata = notification.Metadata
                    })
                };
                
                var response = await _notificationService.SendNotification(request);
                
                _logger.LogInformation("Notification sent successfully via module. UserId: {UserId}, Response: {Response}", 
                    notification.UserId, response);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending notification to user: {UserId}", notification.UserId);
                return false;
            }
        }
    }
}
