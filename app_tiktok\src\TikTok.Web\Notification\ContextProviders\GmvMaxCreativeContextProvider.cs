using System;
using TikTok.Application.Contracts.Notification;
using TikTok.Consts;
using TikTok.Enums;
using Volo.Abp.DependencyInjection;
using Newtonsoft.Json;

namespace TikTok.Web.Notification.ContextProviders
{
    /// <summary>
    /// Context provider for GMV Max Creative notifications
    /// </summary>
    public class GmvMaxCreativeContextProvider : ITransientDependency
    {
        public string Context => TikTokNotificationConst.GmvMaxCreativeStatusChange;

        /// <summary>
        /// Build URL for notification routing based on notification data
        /// </summary>
        public string BuildUrl(string objectId, string payload = null)
        {
            if (string.IsNullOrEmpty(objectId))
            {
                return "/campaigns";
            }

            var baseUrl = $"/campaigns/{objectId}";

            // Parse payload for additional routing information
            if (!string.IsNullOrEmpty(payload))
            {
                try
                {
                    var payloadObj = JsonConvert.DeserializeObject<GmvMaxCreativePayload>(payload);
                    if (payloadObj != null)
                    {
                        switch (payloadObj.UserType)
                        {
                            case TikTokUserType.CAMPAIGN_MANAGER:
                                return $"{baseUrl}/creatives?tab=disabled&view=manager";
                                
                            case TikTokUserType.CREATIVE_APPROVER:
                                return $"{baseUrl}/creatives?tab=review&filter=disabled";
                                
                            case TikTokUserType.ADVERTISER:
                                return $"{baseUrl}?tab=performance&alert=creative-disabled";
                        }
                    }
                }
                catch (JsonException)
                {
                    // Log error if needed, fallback to default URL
                }
            }

            return $"{baseUrl}/creatives";
        }

        /// <summary>
        /// Get UI component configuration for notification
        /// </summary>
        public object GetUIComponent(string objectId, string context, string metadata = null)
        {
            var component = new
            {
                Type = "CreativeStatusAlert",
                Config = new
                {
                    CampaignId = objectId,
                    Context = context,
                    ShowActions = true,
                    EnableQuickActions = true,
                    Metadata = metadata
                }
            };

            return component;
        }
    }

    /// <summary>
    /// Payload structure for GMV Max Creative notifications
    /// </summary>
    public class GmvMaxCreativePayload
    {
        public TikTokUserType UserType { get; set; }
        public int DisabledCount { get; set; }
        public int TotalCount { get; set; }
        public string CampaignName { get; set; }
        public DateTime Timestamp { get; set; }
    }
}
