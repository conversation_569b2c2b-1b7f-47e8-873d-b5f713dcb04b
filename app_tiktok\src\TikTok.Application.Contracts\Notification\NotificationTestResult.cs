using System;
using System.Collections.Generic;

namespace TikTok.Application.Contracts.Notification
{
    /// <summary>
    /// Kết quả test notification
    /// </summary>
    public class NotificationTestResult
    {
        public string NotificationType { get; set; }
        public string CampaignId { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public TimeSpan Duration { get; set; }
        public bool Success { get; set; }
        public string ErrorMessage { get; set; }
        public string ErrorCode { get; set; }

        // GMV Max specific results
        public int TotalAdvertisers { get; set; }
        public int AdvertisersProcessed { get; set; }
        public int DaysProcessed { get; set; }
        public int TotalRecords { get; set; }
        public int NewRecords { get; set; }
        public int UpdatedRecords { get; set; }
        public int CampaignsProcessed { get; set; }
        public int StoresProcessed { get; set; }
        public int ProductsProcessed { get; set; }
        public int CreativesProcessed { get; set; }
        public int NotificationsSent { get; set; }
        public List<string> CampaignsWithNotifications { get; set; } = new List<string>();

        // Future: Có thể thêm các loại notification khác
        // public int BudgetAlertsSent { get; set; }
        // public int PerformanceAlertsSent { get; set; }
    }
}
