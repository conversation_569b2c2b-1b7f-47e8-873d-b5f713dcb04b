@page
@using Microsoft.AspNetCore.Mvc.Localization
@using TikTok.Localization
@using Volo.Abp.Users
@model TikTok.Web.Pages.Dashboard.IndexModel
@inject IHtmlLocalizer<TikTokResource> L
@inject ICurrentUser CurrentUser
@section styles {
    <abp-style src="/Pages/Index.css" />
}
@section scripts {
    <abp-script src="/Pages/Index.js" />
}

<div class="container" id="home">
    @if (!CurrentUser.IsAuthenticated)
    {
        <!-- Welcome message for non-authenticated users -->
        <div class="p-5 text-center">
            <div class="d-inline-block bg-primary text-white p-1 h5 rounded mb-4" role="alert">
                <h5 class="m-1"> <i class="fas fa-lock"></i> Chào mừng đến với <strong>Hệ thống Quản lý Tài khoản Quảng cáo
                        TikTok</strong>!</h5>
            </div>
            <h1><PERSON><PERSON>ng nhập để truy cập Dashboard</h1>
            <p class="lead px-lg-5 mx-lg-5">Vui lòng đăng nhập để xem tổng quan hệ thống và quản lý tài khoản quảng cáo</p>

            <div class="mt-4">
                <a abp-button="Primary" href="~/Account/Login" class="btn btn-primary btn-lg">
                    <i class="fa fa-sign-in"></i> @L["Login"]
                </a>
            </div>
        </div>

        <!-- System Information for non-authenticated users -->
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-info-circle"></i> Thông tin hệ thống</h5>
                    </div>
                    <div class="card-body text-center">
                        <p class="mb-2"><strong>Phiên bản:</strong> TikTok API Management System v1.0</p>
                        <p class="mb-0"><strong>Hỗ trợ:</strong> Quản lý tài khoản quảng cáo TikTok</p>
                    </div>
                </div>
            </div>
        </div>
    }
    else
    {
        <!-- Dashboard for authenticated users -->
        <div class="text-center">
            <div class="d-inline-block bg-success text-white p-1 h5 rounded mb-4 " role="alert">
                <h5 class="m-1"> <i class="fas fa-rocket"></i> Chào mừng đến với <strong>Hệ thống Quản lý Tài khoản Quảng
                        cáo TikTok</strong>!</h5>
            </div>
        </div>

        <!-- Overview Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    Trung tâm kinh doanh
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="businessCenterCount">
                                    <i class="fas fa-spinner fa-spin"></i>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-building fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    Tài khoản quảng cáo
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="adAccountCount">
                                    <i class="fas fa-spinner fa-spin"></i>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-ad fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-left-info shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                    Chiến dịch
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="campaignCount">
                                    <i class="fas fa-spinner fa-spin"></i>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-bullhorn fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-left-warning shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                    Khách hàng
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="customerCount">
                                    <i class="fas fa-spinner fa-spin"></i>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-users fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Last Updated Info -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center">
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">
                                <i class="fas fa-clock"></i> Cập nhật lần cuối: <span id="lastUpdated">Đang tải...</span>
                            </small>
                            <button id="refreshBtn" class="btn btn-sm btn-outline-primary" onclick="refreshDashboard()">
                                <i class="fas fa-sync-alt"></i> Làm mới
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-bolt"></i> Thao tác nhanh</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-auto justify-content-center mb-2" id="businessCenterBtn"
                                style="display: none;">
                                <a href="~/BusinessCenters" class="btn btn-primary me-2">
                                    <i class="fas fa-building"></i> Quản lý Trung tâm kinh doanh
                                </a>
                            </div>
                            <div class="col-md-auto justify-content-center mb-2" id="adAccountBtn"
                                style="display: none;">
                                <a href="~/AdAccounts" class="btn btn-success me-2">
                                    <i class="fas fa-ad"></i> Quản lý Tài khoản quảng cáo
                                </a>
                            </div>
                            <div class="col-md-auto justify-content-center mb-2" id="balanceBusinessCenterBtn"
                                style="display: none;">
                                <a href="~/BalanceBusinessCenters" class="btn btn-warning me-2">
                                    <i class="fas fa-wallet"></i> Ngân sách Trung tâm kinh doanh
                                </a>
                            </div>
                            <div class="col-md-auto justify-content-center mb-2" id="balanceAdAccountBtn"
                                style="display: none;">
                                <a href="~/BalanceAdAccounts" class="btn btn-info me-2">
                                    <i class="fas fa-credit-card"></i> Ngân sách Tài khoản quảng cáo
                                </a>
                            </div>
                            <div class="col-md-auto justify-content-center mb-2" id="transactionBtn"
                                style="display: none;">
                                <a href="~/Transactions" class="btn btn-secondary me-2">
                                    <i class="fas fa-exchange-alt"></i> Quản lý Giao dịch
                                </a>
                            </div>
                            <div class="col-md-auto justify-content-center mb-2" id="assetBtn"
                                style="display: none;">
                                <a href="~/Assets" class="btn btn-primary me-2">
                                    <i class="fas fa-cube"></i> Quản lý Tài sản
                                </a>
                            </div>
                            <div class="col-md-auto justify-content-center mb-2" id="workflowStudioBtn"
                                style="display: none;">
                                <a href="~/workflows-studio" class="btn btn-danger me-2">
                                    <i class="fa fa-rocket"></i> Elsa Workflows Studio
                                </a>
                            </div>
                            <div class="col-md-auto justify-content-center mb-2" id="notificationRulesBtn"
                                style="display: none;">
                                <a href="~/NotificationRules" class="btn btn-purple me-2" style="background-color: #6f42c1; border-color: #6f42c1; color: white;">
                                    <i class="fas fa-bell"></i> Quản lý Quy tắc thông báo
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Information -->
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-user"></i> Thông tin người dùng</h5>
                    </div>
                    <div class="card-body">
                        <p class="mb-2"><strong>Tên:</strong> <span id="userName">Đang tải...</span></p>
                        <p class="mb-2"><strong>Email:</strong> <span id="userEmail">Đang tải...</span></p>
                        <p class="mb-2"><strong>Vai trò:</strong> <span id="userRoles">Đang tải...</span></p>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-info-circle"></i> Thông tin hệ thống</h5>
                    </div>
                    <div class="card-body">
                        <p class="mb-2"><strong>Phiên bản:</strong> TikTok API Management System v1.0</p>
                        <p class="mb-2"><strong>Trạng thái:</strong> <span class="badge bg-success">Hoạt động</span></p>
                        <p class="mb-0"><strong>Hỗ trợ:</strong> Quản lý tài khoản quảng cáo TikTok</p>
                    </div>
                </div>
            </div>
        </div>
    }

</div>

