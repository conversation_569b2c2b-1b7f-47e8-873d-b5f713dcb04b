using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using TikTok.Application.Contracts.Notification;

namespace TikTok.HttpApi.Controllers
{
    /// <summary>
    /// Controller phục vụ màn NotificationTest (Monitor) – test gửi thông báo theo CampaignId
    /// </summary>
    [Route("/notification-test")] // Razor Page JS đang gọi /notification-test/test
    [ApiController]
    [Authorize]
    public class NotificationMonitorController : ControllerBase
    {
        private readonly INotificationTestService _notificationTestService;
        private readonly ILogger<NotificationMonitorController> _logger;

        public NotificationMonitorController(
            INotificationTestService notificationTestService,
            ILogger<NotificationMonitorController> logger)
        {
            _notificationTestService = notificationTestService;
            _logger = logger;
        }

        /// <summary>
        /// Test notification theo CampaignId (dùng bởi trang /notification-test/monitor)
        /// </summary>
        /// <param name="campaignId">ID chiến dịch</param>
        /// <param name="notificationType">Loại notification, mặc định GmvMaxCreativeStatusChange</param>
        /// <param name="maxDays">Số ngày gần nhất để lấy dữ liệu</param>
        [HttpPost("test")]
        public async Task<IActionResult> Test([FromForm] string campaignId, [FromForm] string notificationType = "GmvMaxCreativeStatusChange", [FromForm] int maxDays = 1)
        {
            if (string.IsNullOrWhiteSpace(campaignId))
            {
                return BadRequest(new { success = false, message = "campaignId is required" });
            }

            try
            {
                _logger.LogInformation("NotificationTest Monitor: Start test. CampaignId={CampaignId}, Type={Type}, MaxDays={MaxDays}", campaignId, notificationType, maxDays);
                var result = await _notificationTestService.TestNotificationAsync(campaignId, notificationType, maxDays);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "NotificationTest Monitor: Error testing notification");
                return BadRequest(new { success = false, message = ex.Message });
            }
        }
    }
}


