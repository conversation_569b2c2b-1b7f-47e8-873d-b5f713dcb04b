using System.Collections.Generic;
using System.Threading.Tasks;
using TikTok.Enums;
using Volo.Abp.DependencyInjection;

namespace TikTok.Application.Contracts.Notification
{
    /// <summary>
    /// Core interface for building TikTok-specific notifications
    /// Simplified approach without GenericFactory dependency
    /// </summary>
    public interface ITikTokNotificationBuildProvider : ITransientDependency
    {
        /// <summary>
        /// Context identifier for this notification builder
        /// </summary>
        string Context { get; }

        /// <summary>
        /// User types allowed to receive notifications from this builder
        /// </summary>
        IEnumerable<TikTokUserType> AllowSendToUserTypes { get; }

        /// <summary>
        /// Get notification template for specific user type
        /// </summary>
        Task<string> GetTemplateTitle(TikTokUserType userType, TikTokEntityType? entityType = null);

        /// <summary>
        /// Build notifications for an object (campaignId, etc.)
        /// </summary>
        Task<IEnumerable<TikTokNotificationDto>> BuildNotifications(string objectId);

        /// <summary>
        /// Build notifications for specific users
        /// </summary>
        Task<IEnumerable<TikTokNotificationDto>> BuildNotificationByUsers(string objectId, IEnumerable<TikTokNotificationUserDto> users);
    }
}
