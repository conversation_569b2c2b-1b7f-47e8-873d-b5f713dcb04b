using System;
using TikTok.Enums;

namespace TikTok.Application.Contracts.Notification
{
    /// <summary>
    /// Thông tin về việc thay đổi trạng thái creative
    /// </summary>
    public class CreativeStatusChangeInfo
    {
        /// <summary>
        /// ID của campaign
        /// </summary>
        public string CampaignId { get; set; }

        /// <summary>
        /// ID của creative (ItemId)
        /// </summary>
        public string CreativeId { get; set; }

        /// <summary>
        /// Trạng thái cũ
        /// </summary>
        public CreativeDeliveryStatus? OldStatus { get; set; }

        /// <summary>
        /// Trạng thái mới
        /// </summary>
        public CreativeDeliveryStatus? NewStatus { get; set; }

        /// <summary>
        /// ID của advertiser
        /// </summary>
        public string AdvertiserId { get; set; }

        /// <summary>
        /// ID của Business Center
        /// </summary>
        public string BcId { get; set; }

        /// <summary>
        /// Thời gian thay đổi
        /// </summary>
        public DateTime ChangeTime { get; set; }

        /// <summary>
        /// Tên campaign (để hiển thị)
        /// </summary>
        public string CampaignName { get; set; }
    }
}
