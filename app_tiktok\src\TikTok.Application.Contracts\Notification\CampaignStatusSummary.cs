using System;
using System.Collections.Generic;
using System.Linq;
using TikTok.Enums;

namespace TikTok.Application.Contracts.Notification
{
    /// <summary>
    /// Tổng hợp trạng thái creative theo campaign
    /// </summary>
    public class CampaignStatusSummary
    {
        /// <summary>
        /// ID của campaign
        /// </summary>
        public string CampaignId { get; set; }

        /// <summary>
        /// Tên campaign
        /// </summary>
        public string CampaignName { get; set; }

        /// <summary>
        /// ID của advertiser
        /// </summary>
        public string AdvertiserId { get; set; }

        /// <summary>
        /// ID của Business Center
        /// </summary>
        public string BcId { get; set; }

        /// <summary>
        /// Số lượng creative theo từng trạng thái
        /// </summary>
        public Dictionary<CreativeDeliveryStatus, int> StatusCounts { get; set; } = new();

        /// <summary>
        /// Tổng số creative trong campaign
        /// </summary>
        public int TotalCreatives { get; set; }

        /// <summary>
        /// Danh sách ID của các creative đã thay đổi
        /// </summary>
        public List<string> ChangedCreativeIds { get; set; } = new();

        /// <summary>
        /// Thời gian thay đổi cuối cùng
        /// </summary>
        public DateTime LastChangeTime { get; set; }

        /// <summary>
        /// Số lượng creative có vấn đề (NOT_DELIVERYIN, EXCLUDED, UNAVAILABLE, REJECTED)
        /// </summary>
        public int ProblematicCreativesCount => GetProblematicStatusCount();

        /// <summary>
        /// Kiểm tra xem có creative nào có vấn đề không
        /// </summary>
        public bool HasProblematicStatuses => ProblematicCreativesCount > 0;

        /// <summary>
        /// Lấy số lượng creative có vấn đề
        /// </summary>
        private int GetProblematicStatusCount()
        {
            var problematicStatuses = new[]
            {
                CreativeDeliveryStatus.NOT_DELIVERYIN,
                CreativeDeliveryStatus.EXCLUDED,
                CreativeDeliveryStatus.UNAVAILABLE,
                CreativeDeliveryStatus.REJECTED
            };

            return problematicStatuses.Sum(status => StatusCounts.GetValueOrDefault(status, 0));
        }

        /// <summary>
        /// Lấy số lượng creative theo trạng thái cụ thể
        /// </summary>
        public int GetStatusCount(CreativeDeliveryStatus status)
        {
            return StatusCounts.GetValueOrDefault(status, 0);
        }
    }
}
