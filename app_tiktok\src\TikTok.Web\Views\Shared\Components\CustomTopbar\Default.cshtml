@* Renders custom items in the main topbar *@
<div class="currency-dropdown-container">
    <abp-dropdown id="currencyDropdown" class="currency-dropdown">
        <abp-dropdown-button class="btn btn-sm" text="USD" />
        <abp-dropdown-menu>
            <abp-dropdown-item href="#" data-currency="USD">
                <i class="fa-solid fa-dollar-sign me-2"></i>USD
            </abp-dropdown-item>
            <abp-dropdown-item href="#" data-currency="VND">
                <i class="fa-solid fa-dong-sign me-2"></i>VND
            </abp-dropdown-item>
        </abp-dropdown-menu>
    </abp-dropdown>
</div>
<script>
    (function () {
        try {
            var dropdown = document.getElementById('currencyDropdown');
            if (!dropdown) return;

            var button = dropdown.querySelector('[data-bs-toggle="dropdown"], .dropdown-toggle, abp-dropdown-button');
            // abp-dropdown renders a real button with dropdown-toggle class; fallback to attribute
            if (!button) {
                button = dropdown.querySelector('.dropdown-toggle');
            }

            var items = dropdown.querySelectorAll('[data-currency]');
            if (!button || !items || items.length === 0) return;

            var stored = null;
            try { stored = localStorage.getItem('tiktok_currency'); } catch (e) { }
            if (stored !== 'USD' && stored !== 'VND') {
                stored = 'USD';
            }

            // Initialize button text with icon
            try { 
                var icon = stored === 'USD' ? 'fa-dollar-sign' : 'fa-dong-sign';
                button.innerHTML = `<i class="fa-solid ${icon} me-1"></i>${stored}`;
            } catch (__) { }

            function selectCurrency(newCurrency) {
                if (newCurrency !== 'USD' && newCurrency !== 'VND') return;
                try { localStorage.setItem('tiktok_currency', newCurrency); } catch (err) { }

                // Update button label with icon
                try { 
                    var icon = newCurrency === 'USD' ? 'fa-dollar-sign' : 'fa-dong-sign';
                    button.innerHTML = `<i class="fa-solid ${icon} me-1"></i>${newCurrency}`;
                } catch (__) { }

                // Notify any listeners
                try {
                    var evt = new CustomEvent('currency:changed', { detail: { currency: newCurrency } });
                    window.dispatchEvent(evt);
                } catch (err2) { }

                // Fallback: reload to let pages re-initialize with new currency
                window.location.reload();
            }

            items.forEach(function (el) {
                el.addEventListener('click', function (e) {
                    e.preventDefault();
                    var value = el.getAttribute('data-currency');
                    selectCurrency(value);
                });
            });
        } catch (_) { }
    })();
</script>


