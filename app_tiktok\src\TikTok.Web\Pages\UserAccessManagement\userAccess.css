/* User Access Management Styles */

.user-access-container {
    padding: 20px;
}

/* Modal sizing */
.modal-xxl {
    max-width: 95vw;
}

/* Tall scrollable lists in modal */
.uam-list {
    max-height: none;
    overflow: visible;
}

/* Fit modal height to viewport and keep header/footer fixed */
#AdAccountAccessModal .modal-dialog {
    /* Set Bootstrap width variable; let Bootstrap compute max-width */
    --bs-modal-width: 95vw;
    margin: 1rem auto;
}

#AdAccountAccessModal .modal-content {
    height: 90vh;
    display: flex;
    flex-direction: column;
}

#AdAccountAccessModal .modal-body {
    flex: 1 1 auto;
    overflow: auto;
}

/* Batch Processing Progress */
.btn-processing {
    position: relative;
    overflow: hidden;
}

.btn-processing::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

.btn-processing:disabled {
    opacity: 0.8;
    cursor: not-allowed;
}

/* Progress indicator styles */
.progress-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.progress-indicator .spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Pagination styles */
#paginationContainer {
    margin-top: 1rem;
}

#paginationContainer .pagination {
    margin-bottom: 0.5rem;
}

#paginationContainer .page-link {
    color: #0d6efd;
    border-color: #dee2e6;
    font-size: 0.875rem;
}

#paginationContainer .page-item.active .page-link {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

#paginationContainer .page-item.disabled .page-link {
    color: #6c757d;
    background-color: #fff;
    border-color: #dee2e6;
}

#paginationContainer .page-link:hover {
    color: #0a58ca;
    background-color: #e9ecef;
    border-color: #dee2e6;
}

/* Performance mode styles */
.text-warning {
    color: #f0ad4e !important;
}

.performance-mode {
    border-left: 4px solid #f0ad4e;
    background-color: #fcf8e3;
}

.performance-warning {
    background-color: #f2dede;
    border-color: #ebccd1;
    color: #a94442;
}

.performance-critical {
    background-color: #f2dede;
    border-color: #d9534f;
    color: #a94442;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
    100% {
        opacity: 1;
    }
}

/* DataTable Performance Optimizations */
.table-loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Enhanced DataTable Processing Indicator */
.dataTables_processing {
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    background: rgba(255, 255, 255, 0.95) !important;
    padding: 25px 30px !important;
    border-radius: 12px !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
    z-index: 1050 !important;
    font-weight: 500 !important;
    color: #495057 !important;
    font-size: 14px !important;
    border: 1px solid rgba(0, 123, 255, 0.2) !important;
    backdrop-filter: blur(5px) !important;
    min-width: 200px !important;
    text-align: center !important;
}

.dataTables_processing::before {
    content: '' !important;
    display: inline-block !important;
    width: 24px !important;
    height: 24px !important;
    border: 3px solid #e3f2fd !important;
    border-top: 3px solid #007bff !important;
    border-radius: 50% !important;
    animation: datatable-spin 1s linear infinite !important;
    margin-right: 12px !important;
    vertical-align: middle !important;
}

@keyframes datatable-spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* Optimize DataTable rendering */
.dataTables_wrapper {
    position: relative !important;
    min-height: 200px !important;
}

/* Ensure table container has proper positioning for loading indicator */
#UsersTable {
    position: relative !important;
}

#UsersTable_wrapper {
    position: relative !important;
    min-height: 200px !important;
}

/* Initial loading row styling */
#initial-loading-row {
    background-color: #f8f9fa !important;
    display: table-row !important;
}

#initial-loading-row td {
    border: none !important;
    padding: 40px 20px !important;
}

#initial-loading-row .spinner-border {
    width: 2rem !important;
    height: 2rem !important;
    border-width: 0.25em !important;
}

.dataTables_scrollBody {
    overflow-x: auto;
    overflow-y: visible;
}

/* Reduce DOM complexity */
.dataTables_info,
.dataTables_length,
.dataTables_filter {
    margin-bottom: 1rem;
}

/* Optimize table rendering */
#UsersTable {
    table-layout: fixed;
    width: 100%;
}

#UsersTable th,
#UsersTable td {
    padding: 8px 12px;
    word-wrap: break-word;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Loading skeleton for better UX */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Statistics Cards */
.statistics-card {
    transition: transform 0.2s ease-in-out;
}

.statistics-card:hover {
    transform: translateY(-2px);
}

.statistics-card .card-body {
    padding: 1.5rem;
}

.statistics-card h4 {
    font-weight: bold;
    margin: 0;
}

.statistics-card h6 {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

/* Filter Controls */
.filter-controls {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.filter-controls .form-label {
    font-weight: 500;
    color: #495057;
}

/* Users Table */
#UsersTable {
    font-size: 0.9rem;
}

#UsersTable th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    color: #495057;
}

#UsersTable td {
    vertical-align: middle;
}

/* Role Badges */
.badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
}

/* Action Buttons */
.manage-access-btn {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
}

.manage-access-btn:hover {
    transform: translateY(-1px);
}

/* Ad Account Access Modal */
#AdAccountAccessModal .modal-dialog {
    --bs-modal-width: 95vw;
}

#AdAccountAccessModal .list-group {
    border-radius: 8px;
}

#AdAccountAccessModal .list-group-item {
    border: 1px solid #dee2e6;
    margin-bottom: 2px;
    border-radius: 6px !important;
    transition: all 0.2s ease-in-out;
}

#AdAccountAccessModal .list-group-item:hover {
    background-color: #f8f9fa;
    transform: translateX(2px);
    cursor: pointer;
}

#AdAccountAccessModal .list-group-item.active {
    background-color: #007bff;
    border-color: #007bff;
}

/* Search inputs in modal */
#AdAccountAccessModal .input-group-sm .form-control {
    font-size: 0.875rem;
}

#AdAccountAccessModal .input-group-text {
    background-color: #e9ecef;
    border-color: #ced4da;
}

/* Loading states */
.loading-spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: loading-spinner-spin 1s linear infinite;
}

@keyframes loading-spinner-spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* BC Groups Styling */
.bc-group {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    margin-bottom: 10px;
    background: #f8f9fa;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.bc-group-header {
    background: #f8f9fa;
    padding: 12px 15px;
    border-bottom: 1px solid #dee2e6;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 8px 8px 0 0;
    transition: all 0.2s ease;
    user-select: none;
}

.bc-group-header:hover {
    background: #e9ecef;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.bc-group-header.collapsed {
    border-radius: 8px;
    border-bottom: none;
}

.bc-group-title {
    font-weight: 600;
    color: #495057;
    margin: 0;
    display: flex;
    align-items: center;
    flex: 1;
}

.bc-group-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.bc-group-line {
    font-size: 0.9rem;
    color: #495057;
    font-weight: 500;
    margin: 0;
}

.bc-count-badge {
    background: #007bff;
    color: white;
    padding: 2px 6px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-left: 8px;
}

.bc-group-actions {
    display: flex;
    gap: 5px;
    pointer-events: auto;
}

.bc-group-actions button {
    pointer-events: auto;
}

.bc-group-toggle {
    background: none;
    border: none;
    color: #6c757d;
    font-size: 1.2rem;
    transition: color 0.2s ease;
}

.bc-group-content {
    padding: 0;
    max-height: 500px;
    overflow-y: auto;
    transition: max-height 0.3s ease;
    background: #fff;
}

.bc-group-content.collapsed {
    max-height: 0;
    overflow: hidden;
}

.bc-group-account {
    padding: 8px 15px;
    border-bottom: 1px solid #f1f3f4;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.bc-group-account:last-child {
    border-bottom: none;
}

.bc-group-account:hover {
    background: #f8f9fa;
}

.bc-group-account-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.bc-group-account-line {
    font-size: 0.9rem;
    color: #495057;
    margin: 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .filter-controls {
        padding: 10px;
    }

    .filter-controls .row > div {
        margin-bottom: 10px;
    }

    #AdAccountAccessModal .modal-dialog {
        margin: 10px;
        max-width: calc(100% - 20px);
    }

    .statistics-card .card-body {
        padding: 1rem;
    }

    .statistics-card h4 {
        font-size: 1.5rem;
    }

    /* Mobile DataTable processing indicator */
    .dataTables_processing {
        padding: 20px 25px !important;
        font-size: 13px !important;
        min-width: 180px !important;
    }

    .dataTables_processing::before {
        width: 20px !important;
        height: 20px !important;
        margin-right: 10px !important;
    }
}

@media (max-width: 576px) {
    #UsersTable {
        font-size: 0.8rem;
    }

    .manage-access-btn {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
    }

    .badge {
        font-size: 0.7rem;
        padding: 0.25rem 0.5rem;
    }
}

/* Custom scrollbar for ad accounts lists */
#AvailableAdAccountsList::-webkit-scrollbar,
#AssignedAdAccountsList::-webkit-scrollbar {
    width: 6px;
}

#AvailableAdAccountsList::-webkit-scrollbar-track,
#AssignedAdAccountsList::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

#AvailableAdAccountsList::-webkit-scrollbar-thumb,
#AssignedAdAccountsList::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

#AvailableAdAccountsList::-webkit-scrollbar-thumb:hover,
#AssignedAdAccountsList::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Animation for statistics cards */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

#statisticsCards {
    animation: fadeInUp 0.5s ease-out;
}

/* Hover effects for interactive elements */
.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.card:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Focus states for accessibility */
.form-control:focus,
.form-select:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Error and success states */
.alert {
    border-radius: 8px;
}

.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

.alert-warning {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

/* BC Group Remove Buttons */
.bc-group-remove-all,
.bc-group-remove-single {
    color: #dc3545 !important;
    border-color: #dc3545 !important;
}

.bc-group-remove-all:hover,
.bc-group-remove-single:hover {
    background-color: #dc3545 !important;
    color: white !important;
}

.bc-group-remove-all:focus,
.bc-group-remove-single:focus {
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
}
