using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Application.Contracts.Notification;
using TikTok.Consts;
using TikTok.Enums;
using Volo.Abp.DependencyInjection;

namespace TikTok.Application.Notification
{
    /// <summary>
    /// Abstract base class for TikTok notification builders
    /// Simplified approach without GenericFactory dependency
    /// </summary>
    public abstract class TikTokNotificationBuildBase : ITikTokNotificationBuildProvider
    {
        protected readonly IAbpLazyServiceProvider LazyServiceProvider;
        protected readonly ILogger Logger;

        protected TikTokNotificationBuildBase(IAbpLazyServiceProvider abpLazyServiceProvider)
        {
            LazyServiceProvider = abpLazyServiceProvider;
            Logger = LazyServiceProvider.LazyGetRequiredService<ILogger<TikTokNotificationBuildBase>>();
        }

        /// <summary>
        /// Context identifier for this notification builder
        /// Must be implemented by derived classes
        /// </summary>
        public abstract string Context { get; }

        /// <summary>
        /// User types allowed to receive notifications from this builder
        /// Must be implemented by derived classes
        /// </summary>
        public abstract IEnumerable<TikTokUserType> AllowSendToUserTypes { get; }

        /// <summary>
        /// Get notification template for specific user type
        /// Must be implemented by derived classes
        /// </summary>
        public abstract Task<string> GetTemplateTitle(TikTokUserType userType, TikTokEntityType? entityType = null);

        /// <summary>
        /// Build notifications for an object
        /// Default implementation that can be overridden
        /// </summary>
        public virtual async Task<IEnumerable<TikTokNotificationDto>> BuildNotifications(string objectId)
        {
            try
            {
                // Get entity data (Campaign, Creative, etc.)
                var entityData = await GetEntityData(objectId);
                if (entityData == null)
                {
                    Logger.LogWarning("Entity not found: {ObjectId}", objectId);
                    return Enumerable.Empty<TikTokNotificationDto>();
                }

                // Get notification users based on entity and user types
                var users = await GetNotificationUsers(entityData);

                return await BuildNotificationByUsers(objectId, users);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error building notifications for {ObjectId}", objectId);
                throw;
            }
        }

        /// <summary>
        /// Build notifications for specific users
        /// Default implementation that can be overridden
        /// </summary>
        public virtual async Task<IEnumerable<TikTokNotificationDto>> BuildNotificationByUsers(string objectId, IEnumerable<TikTokNotificationUserDto> users)
        {
            try
            {
                var entityData = await GetEntityData(objectId);
                if (entityData == null)
                {
                    return Enumerable.Empty<TikTokNotificationDto>();
                }

                var notifications = new List<TikTokNotificationDto>();

                foreach (var user in users.Where(u => AllowSendToUserTypes.Contains(u.UserType)))
                {
                    var title = await BuildNotificationTitle(entityData, user.UserType);
                    var content = await BuildNotificationContent(entityData, user.UserType);
                    var payload = await BuildNotificationPayload(entityData, user.UserType);

                    notifications.Add(new TikTokNotificationDto
                    {
                        UserId = user.UserId,
                        UserType = user.UserType,
                        ObjectId = objectId,
                        Context = GetNotificationContext(),
                        Title = title,
                        Content = content,
                        Payload = payload,
                        PhoneNumber = user.PhoneNumber,
                        AdAccountId = user.AdAccountId,
                        BcId = user.BcId
                    });
                }

                return notifications;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error building notifications for users");
                throw;
            }
        }

        /// <summary>
        /// Build a short, dynamic title. Default falls back to template-by-role.
        /// Builders can override to inject entity-driven values (e.g., campaign name/counts).
        /// </summary>
        protected virtual Task<string> BuildNotificationTitle(object entityData, TikTokUserType userType)
        {
            return GetTemplateTitle(userType);
        }

        /// <summary>
        /// Get entity data for notification building
        /// Must be implemented by derived classes
        /// </summary>
        protected abstract Task<object> GetEntityData(string objectId);

        /// <summary>
        /// Get users who should receive notifications
        /// Must be implemented by derived classes
        /// </summary>
        protected abstract Task<IEnumerable<TikTokNotificationUserDto>> GetNotificationUsers(object entityData);

        /// <summary>
        /// Build notification content for specific user type
        /// Can be overridden by derived classes
        /// </summary>
        protected virtual Task<string> BuildNotificationContent(object entityData, TikTokUserType userType)
        {
            // Default implementation - can be overridden
            return Task.FromResult($"Notification content for {userType}");
        }

        /// <summary>
        /// Build notification payload for specific user type
        /// Can be overridden by derived classes
        /// </summary>
        protected virtual Task<string> BuildNotificationPayload(object entityData, TikTokUserType userType)
        {
            // Default implementation - can be overridden
            return Task.FromResult("{}");
        }

        /// <summary>
        /// Get notification context for this builder
        /// Must be implemented by derived classes
        /// </summary>
        protected abstract string GetNotificationContext();

        /// <summary>
        /// Replace template placeholders with actual values
        /// Helper method for derived classes
        /// </summary>
        protected string ReplaceTemplatePlaceholders(string template, Dictionary<string, string> replacements)
        {
            var result = template;
            foreach (var replacement in replacements)
            {
                result = result.Replace(replacement.Key, replacement.Value);
            }
            return result;
        }
    }
}
