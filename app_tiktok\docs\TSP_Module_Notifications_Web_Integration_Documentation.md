# Hướng dẫn Tùy chỉnh TSP.Module.Notifications.Web

## Tổng quan

Tài liệu này hướng dẫn từng bước cách tùy chỉnh và xây dựng workflow hiển thị thông báo sử dụng TSP.Module.Notifications.Web trong ứng dụng ASP.NET Core MVC. Tập trung vào việc tạo custom notification UI workflows cho TikTok project với cấu trúc Views.

## Workflow Thông báo

```
Notification Creation → Context Provider → View Component → UI Display
```

## Mục lục

1. [Thiết lập Module cơ bản](#thiết-lập-module-cơ-bản)
2. [Tạo Custom Context Providers](#tạo-custom-context-providers)
3. [Xây dựng Custom View Components](#xây-dựng-custom-view-components)
4. [Tạo Notification Display Patterns](#tạo-notification-display-patterns)
5. [Tích hợp vào ASP.NET Core MVC Views](#tích-hợp-vào-aspnet-core-mvc-views)
6. [Tùy chỉnh UI và Styling](#tùy-chỉnh-ui-và-styling)
7. [Workflow Examples](#workflow-examples)
8. [Best Practices](#best-practices)

## Thiết lập Module cơ bản

### Bước 1: Đăng ký Module Dependency

Trong file Web Module của bạn (ví dụ: `TikTokWebModule.cs`), thêm `NotificationsWebModule` vào dependencies:

```csharp
[DependsOn(
    typeof(AbpAspNetCoreMvcUiThemeSharedModule),
    typeof(AbpAutoMapperModule),
    typeof(NotificationsWebModule), // ← Thêm dependency này
    typeof(AbpIdentityWebModule),
    typeof(AbpAccountPublicWebModule)
    // ... các dependencies khác
)]
public class TikTokWebModule : AbpModule
{
    // ...
}
```

### Bước 2: Cấu hình Services cơ bản

Trong method `ConfigureServices` của Web Module:

```csharp
public override void ConfigureServices(ServiceConfigurationContext context)
{
    var services = context.Services;
    var configuration = context.Services.GetConfiguration();

    // Cấu hình cơ bản cho notifications
    ConfigureNotifications(context);

    // ... các cấu hình khác
}

private void ConfigureNotifications(ServiceConfigurationContext context)
{
    // Sẽ đăng ký custom context providers ở đây
    // (Chi tiết trong phần tiếp theo)
}
```

### Bước 3: Hiểu về Notification Workflow

TSP.Module.Notifications.Web cung cấp infrastructure cơ bản, bạn cần tùy chỉnh:

1. **Context Providers** - Định nghĩa cách xử lý từng loại thông báo
2. **View Components** - Tùy chỉnh cách hiển thị thông báo
3. **UI Templates** - Tạo giao diện người dùng
4. **Toolbar Integration** - Tích hợp vào layout chính

## Tạo Custom Context Providers

Context Providers định nghĩa cách xử lý từng loại thông báo cụ thể. Mỗi provider sẽ xử lý một context (loại thông báo) riêng biệt.

### Bước 1: Tạo Base Provider

Tạo file `Providers/BaseNotificationProvider.cs`:

```csharp
using Volo.Abp.AspNetCore.Mvc;

namespace TikTok.Web.Providers
{
    public abstract class BaseNotificationProvider
    {
        /// <summary>
        /// Component hiển thị nội dung thông báo (null = sử dụng default)
        /// </summary>
        public virtual Type? BodyComponent => null;

        /// <summary>
        /// Component hiển thị footer thông báo
        /// </summary>
        public virtual Type? FooterComponent => typeof(NotificationFooterViewComponent);
    }
}
```

### Bước 2: Tạo Specific Context Providers

#### Video Notification Provider
Tạo file `Providers/VideoNotificationContextProvider.cs`:

```csharp
using TSP.Module.Notifications.Dtos;
using TSP.Module.Notifications.Providers;

namespace TikTok.Web.Providers
{
    public class VideoNotificationContextProvider : BaseNotificationProvider, INotificationContextProvider
    {
        public string Context => "VideoUpload"; // Định danh loại thông báo

        public string BuildUrl(NotificationDto notification)
        {
            // Xây dựng URL để điều hướng khi click vào thông báo
            if (notification.ObjectId != null)
            {
                return $"/videos/{notification.ObjectId}";
            }
            return "/videos"; // Fallback URL
        }
    }
}
```

#### User Notification Provider
Tạo file `Providers/UserNotificationContextProvider.cs`:

```csharp
namespace TikTok.Web.Providers
{
    public class UserNotificationContextProvider : BaseNotificationProvider, INotificationContextProvider
    {
        public string Context => "UserActivity";

        public string BuildUrl(NotificationDto notification)
        {
            switch (notification.Type)
            {
                case "Follow":
                    return $"/profile/{notification.ObjectId}";
                case "Like":
                    return $"/videos/{notification.ObjectId}";
                case "Comment":
                    return $"/videos/{notification.ObjectId}#comments";
                default:
                    return "/notifications";
            }
        }
    }
}
```

#### System Notification Provider
Tạo file `Providers/SystemNotificationContextProvider.cs`:

```csharp
namespace TikTok.Web.Providers
{
    public class SystemNotificationContextProvider : BaseNotificationProvider, INotificationContextProvider
    {
        public string Context => "System";

        public string BuildUrl(NotificationDto notification)
        {
            // System notifications thường không cần điều hướng
            return "/dashboard";
        }

        // Override để sử dụng custom footer cho system notifications
        public override Type? FooterComponent => typeof(SystemNotificationFooterViewComponent);
    }
}
```

### Bước 3: Đăng ký Context Providers

Trong method `ConfigureNotifications` của Web Module:

```csharp
private void ConfigureNotifications(ServiceConfigurationContext context)
{
    // Đăng ký các context providers
    context.Services.AddScoped<INotificationContextProvider, VideoNotificationContextProvider>();
    context.Services.AddScoped<INotificationContextProvider, UserNotificationContextProvider>();
    context.Services.AddScoped<INotificationContextProvider, SystemNotificationContextProvider>();
}
```

## Xây dựng Custom View Components

View Components là cách hiển thị thông báo trong UI. Bạn có thể tùy chỉnh hoàn toàn cách hiển thị.

### Bước 1: Tạo Notification Footer View Component

Tạo file `ViewComponents/NotificationFooterViewComponent.cs`:

```csharp
using Microsoft.AspNetCore.Mvc;
using TSP.Module.Notifications.Dtos;
using Volo.Abp.AspNetCore.Mvc;

namespace TikTok.Web.ViewComponents
{
    public class NotificationFooterViewComponent : AbpViewComponent
    {
        public virtual IViewComponentResult Invoke(NotificationItemDto model)
        {
            return View("~/Views/Shared/Components/NotificationFooter/Default.cshtml", model);
        }
    }
}
```

### Bước 2: Tạo System Notification Footer (Tùy chọn)

Tạo file `ViewComponents/SystemNotificationFooterViewComponent.cs`:

```csharp
namespace TikTok.Web.ViewComponents
{
    public class SystemNotificationFooterViewComponent : AbpViewComponent
    {
        public virtual IViewComponentResult Invoke(NotificationItemDto model)
        {
            return View("~/Views/Shared/Components/SystemNotificationFooter/Default.cshtml", model);
        }
    }
}
```

### Bước 3: Tạo Notification Bell View Component

Tạo file `ViewComponents/NotificationBellViewComponent.cs`:

```csharp
using Microsoft.AspNetCore.Mvc;
using Volo.Abp.AspNetCore.Mvc;

namespace TikTok.Web.ViewComponents
{
    public class NotificationBellViewComponent : AbpViewComponent
    {
        public IViewComponentResult Invoke()
        {
            return View("~/Views/Shared/Components/NotificationBell/Default.cshtml");
        }
    }
}
```

### Bước 4: Tạo Notification List View Component

Tạo file `ViewComponents/NotificationListViewComponent.cs`:

```csharp
using Microsoft.AspNetCore.Mvc;
using TSP.Module.Notifications.Services;
using Volo.Abp.AspNetCore.Mvc;

namespace TikTok.Web.ViewComponents
{
    public class NotificationListViewComponent : AbpViewComponent
    {
        private readonly INotificationAppService _notificationService;

        public NotificationListViewComponent(INotificationAppService notificationService)
        {
            _notificationService = notificationService;
        }

        public async Task<IViewComponentResult> InvokeAsync(int maxCount = 10)
        {
            // Lấy danh sách thông báo chưa đọc
            var notifications = await _notificationService.GetUnreadNotificationsAsync(maxCount);
            return View("~/Views/Shared/Components/NotificationList/Default.cshtml", notifications);
        }
    }
}
```

## Tạo Notification Display Patterns

Tạo các template Views để hiển thị thông báo theo các pattern khác nhau.

### Bước 1: Tạo Notification Footer Template

Tạo thư mục và file `Views/Shared/Components/NotificationFooter/Default.cshtml`:

```html
@using TSP.Module.Notifications.Dtos
@model NotificationItemDto

<div class="notification-footer d-flex justify-content-between align-items-center">
    <small class="text-muted">
        <i class="fa-regular fa-clock me-1"></i>
        @Model.CreationTime.ToString("dd/MM/yyyy HH:mm")
    </small>

    @if (!Model.IsRead)
    {
        <span class="badge bg-primary">Mới</span>
    }
</div>
```

### Bước 2: Tạo System Notification Footer Template

Tạo file `Views/Shared/Components/SystemNotificationFooter/Default.cshtml`:

```html
@using TSP.Module.Notifications.Dtos
@model NotificationItemDto

<div class="system-notification-footer">
    <div class="d-flex justify-content-between align-items-center">
        <small class="text-muted">
            <i class="fa-solid fa-gear me-1"></i>
            Thông báo hệ thống
        </small>
        <small class="text-muted">
            @Model.CreationTime.ToString("HH:mm")
        </small>
    </div>

    @if (Model.Priority == "High")
    {
        <div class="mt-1">
            <span class="badge bg-danger">Quan trọng</span>
        </div>
    }
</div>
```

### Bước 3: Tạo Notification Bell Template

Tạo file `Views/Shared/Components/NotificationBell/Default.cshtml`:

```html
<div class="notification-bell-container position-relative">
    <button class="btn btn-link notification-bell-btn"
            type="button"
            data-bs-toggle="dropdown"
            aria-expanded="false"
            id="notificationBell">
        <i class="fa-solid fa-bell notification-icon"></i>
        <span class="notification-badge position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger"
              id="notificationCount"
              style="display: none;">
            0
        </span>
    </button>

    <div class="dropdown-menu dropdown-menu-end notification-dropdown" style="width: 350px;">
        <!-- Header -->
        <div class="dropdown-header d-flex justify-content-between align-items-center">
            <h6 class="mb-0">
                <i class="fa-solid fa-bell me-2"></i>
                Thông báo
            </h6>
            <button class="btn btn-sm btn-outline-secondary" id="markAllRead">
                <i class="fa-solid fa-check-double me-1"></i>
                Đánh dấu tất cả
            </button>
        </div>
        <div class="dropdown-divider"></div>

        <!-- Notification List -->
        <div id="notificationList" class="notification-list" style="max-height: 400px; overflow-y: auto;">
            <div class="text-center text-muted py-4" id="emptyState">
                <i class="fa-solid fa-bell-slash fa-2x mb-2"></i>
                <p class="mb-0">Không có thông báo mới</p>
            </div>
        </div>

        <!-- Footer -->
        <div class="dropdown-divider"></div>
        <div class="text-center p-2">
            <a href="/notifications" class="btn btn-sm btn-outline-primary">
                <i class="fa-solid fa-list me-1"></i>
                Xem tất cả thông báo
            </a>
        </div>
    </div>
</div>

<!-- Include JavaScript -->
<script src="~/js/notification-bell.js"></script>
```

### Bước 4: Tạo Notification List Template

Tạo file `Views/Shared/Components/NotificationList/Default.cshtml`:

```html
@using TSP.Module.Notifications.Dtos
@model IEnumerable<NotificationItemDto>

@if (Model.Any())
{
    @foreach (var notification in Model)
    {
        <div class="notification-item p-3 border-bottom @(!notification.IsRead ? "bg-light" : "")"
             data-id="@notification.Id"
             data-url="@notification.Url"
             style="cursor: pointer;">

            <div class="d-flex align-items-start">
                <!-- Icon -->
                <div class="notification-icon me-3">
                    @switch (notification.Type)
                    {
                        case "VideoUpload":
                            <i class="fa-solid fa-video text-primary"></i>
                            break;
                        case "UserActivity":
                            <i class="fa-solid fa-user text-success"></i>
                            break;
                        case "System":
                            <i class="fa-solid fa-gear text-warning"></i>
                            break;
                        default:
                            <i class="fa-solid fa-bell text-info"></i>
                            break;
                    }
                </div>

                <!-- Content -->
                <div class="flex-grow-1">
                    <h6 class="mb-1 @(!notification.IsRead ? "fw-bold" : "")">
                        @notification.Title
                    </h6>
                    <p class="mb-1 text-muted small">
                        @notification.Content
                    </p>

                    <!-- Footer Component -->
                    @if (notification.FooterComponent != null)
                    {
                        @await Component.InvokeAsync(notification.FooterComponent.Name, notification)
                    }
                </div>

                <!-- Unread indicator -->
                @if (!notification.IsRead)
                {
                    <div class="notification-dot bg-primary rounded-circle"
                         style="width: 8px; height: 8px; margin-top: 8px;"></div>
                }
            </div>
        </div>
    }
}
else
{
    <div class="text-center text-muted py-4">
        <i class="fa-solid fa-bell-slash fa-2x mb-2"></i>
        <p class="mb-0">Không có thông báo</p>
    </div>
}
```

## Tích hợp vào ASP.NET Core MVC Views

### Bước 1: Tạo Toolbar Contributor

Tạo file `Toolbar/NotificationToolbarContributor.cs`:

```csharp
using Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared.Toolbars;
using Volo.Abp.Localization;
using Volo.Abp.Users;

namespace TikTok.Web.Toolbar
{
    public class NotificationToolbarContributor : IToolbarContributor
    {
        public Task ConfigureToolbarAsync(IToolbarConfigurationContext context)
        {
            if (context.Toolbar.Name == StandardToolbars.Main)
            {
                // Thêm notification bell vào toolbar
                context.Toolbar.Items.Insert(0, new ToolbarItem(typeof(NotificationBellViewComponent))
                {
                    Order = 100
                });
            }
            return Task.CompletedTask;
        }
    }
}
```

### Bước 2: Đăng ký Toolbar Contributor

Trong Web Module, thêm vào method `ConfigureServices`:

```csharp
private void ConfigureToolbar(ServiceConfigurationContext context)
{
    Configure<AbpToolbarOptions>(options =>
    {
        options.Contributors.Add(new NotificationToolbarContributor());
    });
}

public override void ConfigureServices(ServiceConfigurationContext context)
{
    // ... các cấu hình khác
    ConfigureToolbar(context);
}
```

### Bước 3: Tích hợp vào Layout

Trong file `Views/Shared/_Layout.cshtml`, thêm notification bell vào header:

```html
<!-- Trong phần header/navbar -->
<div class="navbar-nav ms-auto">
    <!-- Các items khác -->

    <!-- Notification Bell -->
    @await Component.InvokeAsync("NotificationBell")

    <!-- User menu, etc. -->
</div>
```

### Bước 4: Tạo Notification Page

Tạo file `Views/Notifications/Index.cshtml`:

```html
@{
    ViewData["Title"] = "Thông báo";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fa-solid fa-bell me-2"></i>
                        Tất cả thông báo
                    </h5>
                    <div>
                        <button class="btn btn-outline-secondary btn-sm" id="markAllReadPage">
                            <i class="fa-solid fa-check-double me-1"></i>
                            Đánh dấu tất cả đã đọc
                        </button>
                        <button class="btn btn-outline-danger btn-sm" id="clearAllNotifications">
                            <i class="fa-solid fa-trash me-1"></i>
                            Xóa tất cả
                        </button>
                    </div>
                </div>
                <div class="card-body p-0">
                    <!-- Notification List Component -->
                    @await Component.InvokeAsync("NotificationList", new { maxCount = 50 })
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Pagination -->
<nav aria-label="Notification pagination">
    <ul class="pagination justify-content-center">
        <!-- Pagination items sẽ được generate bởi JavaScript -->
    </ul>
</nav>

<script src="~/js/notification-page.js"></script>
```

### Bước 5: Tạo Controller

Tạo file `Controllers/NotificationsController.cs`:

```csharp
using Microsoft.AspNetCore.Mvc;
using TSP.Module.Notifications.Services;
using Volo.Abp.AspNetCore.Mvc;

namespace TikTok.Web.Controllers
{
    [Route("notifications")]
    public class NotificationsController : AbpController
    {
        private readonly INotificationAppService _notificationService;

        public NotificationsController(INotificationAppService notificationService)
        {
            _notificationService = notificationService;
        }

        [HttpGet]
        public async Task<IActionResult> Index()
        {
            return View();
        }

        [HttpGet("api/unread")]
        public async Task<IActionResult> GetUnreadNotifications(int maxCount = 10)
        {
            try
            {
                var notifications = await _notificationService.GetUnreadNotificationsAsync(maxCount);
                return Json(notifications);
            }
            catch (Exception ex)
            {
                return Json(new { error = ex.Message });
            }
        }

        [HttpPost("api/{id}/read")]
        public async Task<IActionResult> MarkAsRead(string id)
        {
            try
            {
                await _notificationService.MarkAsReadAsync(id);
                return Json(new { success = true });
            }
            catch (Exception ex)
            {
                return Json(new { error = ex.Message });
            }
        }

        [HttpPost("api/mark-all-read")]
        public async Task<IActionResult> MarkAllAsRead()
        {
            try
            {
                await _notificationService.MarkAllAsReadAsync();
                return Json(new { success = true });
            }
            catch (Exception ex)
            {
                return Json(new { error = ex.Message });
            }
        }
    }
}
```

## Tùy chỉnh UI và Styling

### Bước 1: Tạo CSS cho Notification Components

Tạo file `wwwroot/css/notifications.css`:

```css
/* Notification Bell Styles */
.notification-bell-container {
    position: relative;
    overflow: visible;
    z-index: 1000;
}

.notification-bell-btn {
    border: none;
    background: transparent;
    color: #6c757d;
    transition: all 0.3s ease;
    border-radius: 8px;
    padding: 8px;
    position: relative;
}

.notification-bell-btn:hover {
    background: rgba(108, 117, 125, 0.1);
    color: #495057;
    transform: translateY(-1px);
}

.notification-icon {
    font-size: 1.2rem;
    color: inherit;
}

.notification-badge {
    position: absolute;
    top: 2px;
    right: 2px;
    width: 18px;
    height: 18px;
    font-size: 0.65rem;
    border-radius: 50%;
    background: #dc3545;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid white;
    z-index: 10;
    animation: pulse 2s infinite;
    min-width: 18px;
    line-height: 1;
    font-weight: bold;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Dropdown Styles */
.notification-dropdown {
    border: 1px solid #e9ecef;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    padding: 0;
    margin-top: 8px;
    min-width: 350px;
    max-width: 400px;
}

.notification-dropdown .dropdown-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px 12px 0 0;
    padding: 16px;
    border-bottom: 1px solid #e9ecef;
    font-weight: 600;
}

/* Notification Item Styles */
.notification-item {
    transition: all 0.2s ease;
    cursor: pointer;
    border-radius: 0;
    position: relative;
}

.notification-item:hover {
    background: #f8f9fa !important;
    transform: translateX(2px);
}

.notification-item:last-child {
    border-bottom: none !important;
    border-radius: 0 0 12px 12px;
}

.notification-item.unread {
    background: linear-gradient(90deg, rgba(13, 110, 253, 0.05) 0%, rgba(255, 255, 255, 1) 100%);
    border-left: 3px solid #0d6efd;
}

.notification-dot {
    margin-top: 8px;
    margin-left: 8px;
    box-shadow: 0 0 0 2px white;
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
}

.notification-icon.video {
    background: rgba(13, 110, 253, 0.1);
    color: #0d6efd;
}

.notification-icon.user {
    background: rgba(25, 135, 84, 0.1);
    color: #198754;
}

.notification-icon.system {
    background: rgba(255, 193, 7, 0.1);
    color: #ffc107;
}

/* Footer Styles */
.notification-footer {
    padding: 8px 0;
    border-top: 1px solid #f1f3f4;
    margin-top: 8px;
}

.system-notification-footer {
    background: rgba(255, 193, 7, 0.05);
    padding: 8px 12px;
    border-radius: 6px;
    margin-top: 8px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .notification-dropdown {
        min-width: 300px;
        max-width: 90vw;
        margin-right: 10px;
    }

    .notification-bell-btn {
        padding: 6px;
    }

    .notification-icon {
        font-size: 1rem;
    }

    .notification-badge {
        width: 16px;
        height: 16px;
        font-size: 0.6rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .notification-bell-btn {
        color: #adb5bd !important;
    }

    .notification-bell-btn:hover {
        background: rgba(173, 181, 189, 0.1) !important;
        color: #dee2e6 !important;
    }

    .notification-dropdown {
        background: #212529;
        border-color: #495057;
    }

    .notification-dropdown .dropdown-header {
        background: linear-gradient(135deg, #343a40 0%, #495057 100%);
        border-color: #495057;
        color: #fff;
    }

    .notification-item:hover {
        background: #343a40 !important;
    }

    .notification-item.unread {
        background: linear-gradient(90deg, rgba(13, 110, 253, 0.1) 0%, rgba(33, 37, 41, 1) 100%);
    }
}

/* Loading Animation */
.notification-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

.notification-loading .spinner-border {
    width: 1.5rem;
    height: 1.5rem;
}

/* Empty State */
.notification-empty {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.notification-empty i {
    font-size: 3rem;
    margin-bottom: 16px;
    opacity: 0.5;
}
```

### Bước 2: Tạo JavaScript cho Notification Bell

Tạo file `wwwroot/js/notification-bell.js`:

```javascript
// Notification Bell JavaScript
class NotificationBell {
    constructor() {
        this.bell = document.getElementById('notificationBell');
        this.count = document.getElementById('notificationCount');
        this.list = document.getElementById('notificationList');
        this.markAllRead = document.getElementById('markAllRead');
        this.emptyState = document.getElementById('emptyState');

        this.init();
    }

    init() {
        if (!this.bell || !this.count || !this.list) return;

        // Load notifications on page load
        this.loadNotifications();

        // Set up event listeners
        this.markAllRead?.addEventListener('click', () => this.markAllAsRead());

        // Auto-refresh every 30 seconds
        setInterval(() => this.loadNotifications(), 30000);

        // Handle dropdown show event
        this.bell.addEventListener('shown.bs.dropdown', () => {
            this.loadNotifications();
        });
    }

    async loadNotifications() {
        try {
            this.showLoading();

            const response = await fetch('/notifications/api/unread?maxCount=10');
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            this.updateDisplay(data);
        } catch (error) {
            console.error('Error loading notifications:', error);
            this.showError();
        }
    }

    updateDisplay(notifications) {
        // Update badge count
        const unreadCount = notifications.filter(n => !n.isRead).length;
        this.updateBadge(unreadCount);

        // Update notification list
        this.updateList(notifications);
    }

    updateBadge(count) {
        if (count > 0) {
            this.count.textContent = count > 99 ? '99+' : count;
            this.count.style.display = 'block';
            this.bell.classList.add('has-notifications');
        } else {
            this.count.style.display = 'none';
            this.bell.classList.remove('has-notifications');
        }
    }

    updateList(notifications) {
        if (notifications.length === 0) {
            this.showEmptyState();
            return;
        }

        const html = notifications.map(notification => this.createNotificationHTML(notification)).join('');
        this.list.innerHTML = html;

        // Add click handlers
        this.list.querySelectorAll('.notification-item').forEach(item => {
            item.addEventListener('click', (e) => this.handleNotificationClick(e, item));
        });
    }

    createNotificationHTML(notification) {
        const iconClass = this.getNotificationIcon(notification.type);
        const timeAgo = this.getTimeAgo(notification.creationTime);
        const unreadClass = !notification.isRead ? 'unread' : '';

        return `
            <div class="notification-item p-3 border-bottom ${unreadClass}"
                 data-id="${notification.id}"
                 data-url="${notification.url || '#'}">
                <div class="d-flex align-items-start">
                    <div class="notification-icon ${notification.type} me-3">
                        <i class="${iconClass}"></i>
                    </div>
                    <div class="flex-grow-1">
                        <h6 class="mb-1 ${!notification.isRead ? 'fw-bold' : ''}">${notification.title}</h6>
                        <p class="mb-1 text-muted small">${notification.content}</p>
                        <small class="text-muted">${timeAgo}</small>
                    </div>
                    ${!notification.isRead ? '<div class="notification-dot bg-primary rounded-circle"></div>' : ''}
                </div>
            </div>
        `;
    }

    getNotificationIcon(type) {
        const icons = {
            'VideoUpload': 'fa-solid fa-video',
            'UserActivity': 'fa-solid fa-user',
            'System': 'fa-solid fa-gear',
            'default': 'fa-solid fa-bell'
        };
        return icons[type] || icons.default;
    }

    getTimeAgo(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffInMinutes = Math.floor((now - date) / (1000 * 60));

        if (diffInMinutes < 1) return 'Vừa xong';
        if (diffInMinutes < 60) return `${diffInMinutes} phút trước`;

        const diffInHours = Math.floor(diffInMinutes / 60);
        if (diffInHours < 24) return `${diffInHours} giờ trước`;

        const diffInDays = Math.floor(diffInHours / 24);
        return `${diffInDays} ngày trước`;
    }

    async handleNotificationClick(event, item) {
        event.preventDefault();

        const notificationId = item.dataset.id;
        const url = item.dataset.url;

        // Mark as read
        if (!item.classList.contains('read')) {
            await this.markAsRead(notificationId);
            item.classList.remove('unread');
            item.classList.add('read');

            // Update badge
            const currentCount = parseInt(this.count.textContent) || 0;
            this.updateBadge(Math.max(0, currentCount - 1));
        }

        // Navigate to URL
        if (url && url !== '#') {
            window.location.href = url;
        }
    }

    async markAsRead(notificationId) {
        try {
            await fetch(`/notifications/api/${notificationId}/read`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            });
        } catch (error) {
            console.error('Error marking notification as read:', error);
        }
    }

    async markAllAsRead() {
        try {
            await fetch('/notifications/api/mark-all-read', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            // Refresh the list
            this.loadNotifications();

            // Show success message
            this.showToast('Đã đánh dấu tất cả thông báo là đã đọc', 'success');
        } catch (error) {
            console.error('Error marking all notifications as read:', error);
            this.showToast('Có lỗi xảy ra. Vui lòng thử lại.', 'error');
        }
    }

    showLoading() {
        this.list.innerHTML = `
            <div class="notification-loading">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Đang tải...</span>
                </div>
            </div>
        `;
    }

    showEmptyState() {
        this.list.innerHTML = `
            <div class="notification-empty">
                <i class="fa-solid fa-bell-slash"></i>
                <p class="mb-0">Không có thông báo mới</p>
            </div>
        `;
    }

    showError() {
        this.list.innerHTML = `
            <div class="notification-empty">
                <i class="fa-solid fa-exclamation-triangle text-warning"></i>
                <p class="mb-0">Không thể tải thông báo</p>
                <button class="btn btn-sm btn-outline-primary mt-2" onclick="notificationBell.loadNotifications()">
                    Thử lại
                </button>
            </div>
        `;
    }

    showToast(message, type = 'info') {
        // Implement toast notification (using Bootstrap Toast or custom implementation)
        if (window.abp && window.abp.notify) {
            window.abp.notify[type](message);
        } else {
            alert(message); // Fallback
        }
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.notificationBell = new NotificationBell();
});
```

### Bước 3: Include CSS và JS trong Layout

Trong file `Views/Shared/_Layout.cshtml`, thêm:

```html
<head>
    <!-- Existing head content -->

    <!-- Notification Styles -->
    <link href="~/css/notifications.css" rel="stylesheet" />
</head>

<body>
    <!-- Existing body content -->

    <!-- Before closing body tag -->
    <script src="~/js/notification-bell.js"></script>
</body>
```

## Workflow Examples

### Example 1: Video Upload Notification Workflow

#### Bước 1: Tạo Notification khi Video được upload

```csharp
// Trong VideoService hoặc VideoController
public async Task<IActionResult> UploadVideo(VideoUploadDto input)
{
    try
    {
        // Upload video logic
        var video = await _videoService.CreateAsync(input);

        // Tạo notification
        await _notificationService.CreateAsync(new CreateNotificationDto
        {
            Title = "Video đã được tải lên thành công",
            Content = $"Video '{video.Title}' đã được tải lên và đang được xử lý",
            Type = "VideoUpload",
            Context = "VideoUpload", // Sẽ được xử lý bởi VideoNotificationContextProvider
            ObjectId = video.Id.ToString(),
            UserId = CurrentUser.Id
        });

        return Ok(video);
    }
    catch (Exception ex)
    {
        // Error notification
        await _notificationService.CreateAsync(new CreateNotificationDto
        {
            Title = "Lỗi tải video",
            Content = "Có lỗi xảy ra khi tải video. Vui lòng thử lại.",
            Type = "VideoUpload",
            Context = "VideoUpload",
            Priority = "High",
            UserId = CurrentUser.Id
        });

        throw;
    }
}
```

#### Bước 2: Context Provider xử lý

```csharp
// VideoNotificationContextProvider sẽ tự động xử lý
public string BuildUrl(NotificationDto notification)
{
    if (notification.ObjectId != null)
    {
        return $"/videos/{notification.ObjectId}"; // Điều hướng đến trang video
    }
    return "/videos"; // Fallback
}
```

#### Bước 3: UI hiển thị

Notification sẽ xuất hiện trong notification bell với:
- Icon video (fa-solid fa-video)
- Title: "Video đã được tải lên thành công"
- Content: "Video 'My Video' đã được tải lên và đang được xử lý"
- Click để điều hướng đến `/videos/{videoId}`

### Example 2: User Follow Notification Workflow

#### Bước 1: Tạo Notification khi có người follow

```csharp
// Trong FollowService
public async Task FollowUserAsync(Guid targetUserId)
{
    // Follow logic
    await _followRepository.InsertAsync(new Follow
    {
        FollowerId = CurrentUser.Id.Value,
        TargetUserId = targetUserId
    });

    // Tạo notification cho người được follow
    var follower = await _userRepository.GetAsync(CurrentUser.Id.Value);

    await _notificationService.CreateAsync(new CreateNotificationDto
    {
        Title = "Bạn có người theo dõi mới",
        Content = $"{follower.UserName} đã bắt đầu theo dõi bạn",
        Type = "Follow",
        Context = "UserActivity", // Sẽ được xử lý bởi UserNotificationContextProvider
        ObjectId = follower.Id.ToString(),
        UserId = targetUserId // Gửi cho người được follow
    });
}
```

#### Bước 2: Context Provider xử lý

```csharp
// UserNotificationContextProvider xử lý
public string BuildUrl(NotificationDto notification)
{
    switch (notification.Type)
    {
        case "Follow":
            return $"/profile/{notification.ObjectId}"; // Điều hướng đến profile người follow
        // ... other cases
    }
}
```

### Example 3: System Maintenance Notification Workflow

#### Bước 1: Tạo System Notification

```csharp
// Trong AdminController hoặc Background Service
public async Task NotifySystemMaintenance()
{
    // Lấy tất cả users
    var users = await _userRepository.GetListAsync();

    foreach (var user in users)
    {
        await _notificationService.CreateAsync(new CreateNotificationDto
        {
            Title = "Bảo trì hệ thống",
            Content = "Hệ thống sẽ được bảo trì từ 2:00 AM đến 4:00 AM ngày mai",
            Type = "Maintenance",
            Context = "System", // Sẽ được xử lý bởi SystemNotificationContextProvider
            Priority = "High",
            UserId = user.Id
        });
    }
}
```

#### Bước 2: Custom Footer hiển thị

```html
<!-- SystemNotificationFooter/Default.cshtml -->
<div class="system-notification-footer">
    <div class="d-flex justify-content-between align-items-center">
        <small class="text-muted">
            <i class="fa-solid fa-gear me-1"></i>
            Thông báo hệ thống
        </small>
        <span class="badge bg-warning">Quan trọng</span>
    </div>
</div>
```

### Example 4: Real-time Notification với SignalR (Tùy chọn)

#### Bước 1: Tạo SignalR Hub

```csharp
// Hubs/NotificationHub.cs
public class NotificationHub : Hub
{
    public async Task JoinUserGroup(string userId)
    {
        await Groups.AddToGroupAsync(Context.ConnectionId, $"User_{userId}");
    }

    public async Task LeaveUserGroup(string userId)
    {
        await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"User_{userId}");
    }
}
```

#### Bước 2: Gửi real-time notification

```csharp
// Trong NotificationService
public async Task CreateAsync(CreateNotificationDto input)
{
    // Tạo notification trong database
    var notification = await base.CreateAsync(input);

    // Gửi real-time notification qua SignalR
    await _hubContext.Clients.Group($"User_{input.UserId}")
        .SendAsync("ReceiveNotification", notification);

    return notification;
}
```

#### Bước 3: Client-side JavaScript

```javascript
// Trong notification-bell.js, thêm SignalR connection
const connection = new signalR.HubConnectionBuilder()
    .withUrl("/notificationHub")
    .build();

connection.start().then(function () {
    // Join user group
    connection.invoke("JoinUserGroup", currentUserId);

    // Listen for new notifications
    connection.on("ReceiveNotification", function (notification) {
        // Update UI immediately
        notificationBell.addNewNotification(notification);
        notificationBell.updateBadge();
    });
});
```

## Best Practices

### 1. Context Provider Design

#### Tách biệt Context rõ ràng
```csharp
// Tốt - Context cụ thể
public string Context => "VideoUpload";
public string Context => "UserFollow";
public string Context => "SystemMaintenance";

// Không tốt - Context chung chung
public string Context => "General";
public string Context => "Notification";
```

#### URL Building có ý nghĩa
```csharp
public string BuildUrl(NotificationDto notification)
{
    // Luôn kiểm tra null
    if (notification.ObjectId == null)
        return "/dashboard"; // Fallback URL

    // Xây dựng URL có ý nghĩa
    switch (notification.Type)
    {
        case "VideoLike":
            return $"/videos/{notification.ObjectId}#likes";
        case "VideoComment":
            return $"/videos/{notification.ObjectId}#comments";
        case "UserFollow":
            return $"/profile/{notification.ObjectId}";
        default:
            return $"/notifications/{notification.Id}";
    }
}
```

### 2. View Component Organization

#### Tổ chức thư mục rõ ràng
```
Views/
├── Shared/
│   └── Components/
│       ├── NotificationBell/
│       │   └── Default.cshtml
│       ├── NotificationList/
│       │   └── Default.cshtml
│       ├── NotificationFooter/
│       │   └── Default.cshtml
│       └── SystemNotificationFooter/
│           └── Default.cshtml
```

#### Tái sử dụng Components
```csharp
// Base component cho common functionality
public abstract class BaseNotificationViewComponent : AbpViewComponent
{
    protected string GetTimeAgo(DateTime dateTime)
    {
        var timeSpan = DateTime.Now - dateTime;
        // Implementation...
    }

    protected string GetNotificationIcon(string type)
    {
        // Implementation...
    }
}

// Specific components inherit từ base
public class NotificationFooterViewComponent : BaseNotificationViewComponent
{
    // Specific implementation
}
```

### 3. Performance Optimization

#### Lazy Loading cho Notification List
```javascript
class NotificationBell {
    constructor() {
        this.page = 0;
        this.pageSize = 10;
        this.hasMore = true;
    }

    async loadMoreNotifications() {
        if (!this.hasMore) return;

        const response = await fetch(`/notifications/api/unread?page=${this.page}&size=${this.pageSize}`);
        const data = await response.json();

        if (data.length < this.pageSize) {
            this.hasMore = false;
        }

        this.appendNotifications(data);
        this.page++;
    }
}
```

#### Debounce cho Mark as Read
```javascript
const debouncedMarkAsRead = this.debounce(this.markAsRead.bind(this), 300);

debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
```

#### Caching Strategy
```csharp
public class NotificationService : INotificationService
{
    private readonly IMemoryCache _cache;
    private readonly TimeSpan _cacheExpiry = TimeSpan.FromMinutes(5);

    public async Task<List<NotificationDto>> GetUnreadNotificationsAsync(Guid userId)
    {
        var cacheKey = $"notifications_unread_{userId}";

        if (_cache.TryGetValue(cacheKey, out List<NotificationDto> cachedNotifications))
        {
            return cachedNotifications;
        }

        var notifications = await _repository.GetUnreadNotificationsAsync(userId);
        _cache.Set(cacheKey, notifications, _cacheExpiry);

        return notifications;
    }
}
```

### 4. Error Handling

#### Graceful Degradation
```javascript
async loadNotifications() {
    try {
        const response = await fetch('/notifications/api/unread');
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        const data = await response.json();
        this.updateDisplay(data);
    } catch (error) {
        console.error('Error loading notifications:', error);

        // Fallback to cached data if available
        const cachedData = this.getCachedNotifications();
        if (cachedData) {
            this.updateDisplay(cachedData);
            this.showWarning('Hiển thị dữ liệu đã lưu. Một số thông báo có thể không mới nhất.');
        } else {
            this.showError('Không thể tải thông báo. Vui lòng kiểm tra kết nối mạng.');
        }
    }
}
```

#### Retry Mechanism
```javascript
async fetchWithRetry(url, options = {}, maxRetries = 3) {
    for (let i = 0; i <= maxRetries; i++) {
        try {
            const response = await fetch(url, options);
            if (response.ok) return response;
            throw new Error(`HTTP ${response.status}`);
        } catch (error) {
            if (i === maxRetries) throw error;

            // Exponential backoff
            const delay = Math.pow(2, i) * 1000;
            await new Promise(resolve => setTimeout(resolve, delay));
        }
    }
}
```

### 5. Security Considerations

#### Validate User Access
```csharp
public async Task<IActionResult> MarkAsRead(string notificationId)
{
    // Kiểm tra notification thuộc về user hiện tại
    var notification = await _notificationService.GetAsync(notificationId);
    if (notification.UserId != CurrentUser.Id)
    {
        return Forbid();
    }

    await _notificationService.MarkAsReadAsync(notificationId);
    return Ok();
}
```

#### Sanitize Content
```csharp
public string BuildNotificationContent(string userInput)
{
    // Sanitize HTML content
    return HtmlEncoder.Default.Encode(userInput);
}
```

### 6. Testing Strategy

#### Unit Tests cho Context Providers
```csharp
[Test]
public void VideoNotificationContextProvider_BuildUrl_ReturnsCorrectUrl()
{
    // Arrange
    var provider = new VideoNotificationContextProvider();
    var notification = new NotificationDto { ObjectId = "123" };

    // Act
    var url = provider.BuildUrl(notification);

    // Assert
    Assert.AreEqual("/videos/123", url);
}
```

#### Integration Tests cho API
```csharp
[Test]
public async Task GetUnreadNotifications_ReturnsUserNotifications()
{
    // Arrange
    var userId = Guid.NewGuid();
    await CreateTestNotification(userId);

    // Act
    var response = await Client.GetAsync("/notifications/api/unread");

    // Assert
    response.EnsureSuccessStatusCode();
    var notifications = await response.Content.ReadFromJsonAsync<List<NotificationDto>>();
    Assert.IsNotEmpty(notifications);
}
```

## Tóm tắt Workflow Tích hợp

### Quy trình hoàn chỉnh từ A-Z

```
1. Module Setup
   ├── Đăng ký NotificationsWebModule dependency
   ├── Cấu hình services trong ConfigureServices
   └── Đăng ký toolbar contributor

2. Context Providers
   ├── Tạo base provider class
   ├── Implement specific context providers
   ├── Đăng ký providers trong DI container
   └── Define URL building logic

3. View Components
   ├── Tạo notification view components
   ├── Implement footer components
   ├── Tạo notification bell component
   └── Tạo notification list component

4. UI Templates
   ├── Tạo Razor view templates
   ├── Implement responsive design
   ├── Add accessibility features
   └── Style với CSS

5. Frontend Integration
   ├── Tạo JavaScript handlers
   ├── Implement AJAX calls
   ├── Add real-time updates (optional)
   └── Handle user interactions

6. Testing & Deployment
   ├── Unit tests cho providers
   ├── Integration tests cho API
   ├── UI testing
   └── Performance optimization
```

### Checklist Tích hợp

#### ✅ Module Setup
- [ ] Thêm `NotificationsWebModule` vào `[DependsOn]`
- [ ] Cấu hình `ConfigureNotifications` method
- [ ] Đăng ký toolbar contributor
- [ ] Include CSS và JS files

#### ✅ Context Providers
- [ ] Tạo base provider class
- [ ] Implement video notification provider
- [ ] Implement user notification provider
- [ ] Implement system notification provider
- [ ] Đăng ký tất cả providers trong DI

#### ✅ View Components
- [ ] Tạo `NotificationBellViewComponent`
- [ ] Tạo `NotificationListViewComponent`
- [ ] Tạo `NotificationFooterViewComponent`
- [ ] Tạo `SystemNotificationFooterViewComponent`

#### ✅ UI Templates
- [ ] Tạo notification bell template
- [ ] Tạo notification list template
- [ ] Tạo footer templates
- [ ] Tạo notification page template

#### ✅ Frontend Code
- [ ] Implement `notification-bell.js`
- [ ] Tạo CSS styling
- [ ] Add responsive design
- [ ] Implement error handling

#### ✅ API Integration
- [ ] Tạo notifications controller
- [ ] Implement API endpoints
- [ ] Add authentication/authorization
- [ ] Handle error responses

### File Structure Tổng quan

```
TikTok.Web/
├── Controllers/
│   └── NotificationsController.cs
├── ViewComponents/
│   ├── NotificationBellViewComponent.cs
│   ├── NotificationListViewComponent.cs
│   ├── NotificationFooterViewComponent.cs
│   └── SystemNotificationFooterViewComponent.cs
├── Views/
│   ├── Notifications/
│   │   └── Index.cshtml
│   └── Shared/
│       └── Components/
│           ├── NotificationBell/
│           │   └── Default.cshtml
│           ├── NotificationList/
│           │   └── Default.cshtml
│           ├── NotificationFooter/
│           │   └── Default.cshtml
│           └── SystemNotificationFooter/
│               └── Default.cshtml
├── Providers/
│   ├── BaseNotificationProvider.cs
│   ├── VideoNotificationContextProvider.cs
│   ├── UserNotificationContextProvider.cs
│   └── SystemNotificationContextProvider.cs
├── Toolbar/
│   └── NotificationToolbarContributor.cs
├── wwwroot/
│   ├── css/
│   │   └── notifications.css
│   └── js/
│       └── notification-bell.js
└── TikTokWebModule.cs
```

## Kết luận

Tài liệu này cung cấp hướng dẫn từng bước để tùy chỉnh và triển khai TSP.Module.Notifications.Web trong ứng dụng ASP.NET Core MVC.

### Những gì bạn đã học được:

1. **Workflow hoàn chỉnh** từ notification creation → context provider → view component → UI display
2. **Cách tạo custom context providers** để xử lý các loại thông báo khác nhau
3. **Xây dựng view components** tái sử dụng cho notification display
4. **Tích hợp vào MVC Views** với toolbar và layout
5. **Tùy chỉnh UI/UX** với CSS và JavaScript
6. **Best practices** cho performance, security và maintainability

### Lợi ích của approach này:

- **Modular**: Dễ dàng thêm/sửa/xóa loại thông báo mới
- **Scalable**: Có thể handle nhiều loại notification khác nhau
- **Customizable**: Hoàn toàn tùy chỉnh được UI và logic
- **Maintainable**: Code được tổ chức rõ ràng, dễ bảo trì
- **Testable**: Có thể unit test từng component riêng biệt

### Bước tiếp theo:

1. **Implement theo checklist** được cung cấp
2. **Test thoroughly** với các scenario khác nhau
3. **Optimize performance** dựa trên usage patterns
4. **Monitor và improve** dựa trên user feedback
5. **Extend functionality** khi cần thiết (real-time, push notifications, etc.)

Với hướng dẫn này, bạn có thể triển khai một hệ thống notification hoàn chỉnh, professional và user-friendly cho TikTok project của mình.