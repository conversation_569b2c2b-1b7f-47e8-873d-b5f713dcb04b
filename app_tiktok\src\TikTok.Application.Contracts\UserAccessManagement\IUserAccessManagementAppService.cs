using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TikTok.AdAccounts;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace TikTok.UserAccessManagement
{
    /// <summary>
    /// Interface cho User Access Management App Service
    /// </summary>
    public interface IUserAccessManagementAppService : IApplicationService
    {
        /// <summary>
        /// Lấy danh sách người dùng với thông tin truy cập
        /// </summary>
        /// <param name="input">Thông tin lọc và phân trang</param>
        /// <returns>Danh sách người dùng</returns>
        Task<PagedResultDto<UserAccessDto>> GetUsersAsync(GetUserAccessListDto input);

        /// <summary>
        /// Lấy thông tin truy cập của một người dùng cụ thể
        /// </summary>
        /// <param name="userId">ID người dùng</param>
        /// <returns>Thông tin truy cập</returns>
        Task<UserAccessDto> GetUserAccessAsync(Guid userId);






        /// <summary>
        /// Lấy danh sách tài khoản quảng cáo có thể phân quyền được group theo BC
        /// </summary>
        /// <param name="input">Thông tin tìm kiếm</param>
        /// <returns>Danh sách BC groups với ad accounts</returns>
        Task<List<BcAdAccountGroupDto>> GetAvailableAdAccountsGroupedByBcAsync(GetAvailableAdAccountsDto input);

        /// <summary>
        /// Lấy danh sách tài khoản quảng cáo đã phân quyền được group theo BC
        /// </summary>
        /// <param name="input">Thông tin tìm kiếm</param>
        /// <returns>Danh sách BC groups với ad accounts</returns>
        Task<List<BcAdAccountGroupDto>> GetAssignedAdAccountsGroupedByBcAsync(GetAssignedAdAccountsDto input);

        /// <summary>
        /// Phân quyền tài khoản quảng cáo cho người dùng
        /// </summary>
        /// <param name="input">Thông tin phân quyền</param>
        /// <returns>Kết quả thực hiện</returns>
        Task AssignAdAccountsAsync(AssignAdAccountsDto input);

        /// <summary>
        /// Thu hồi quyền truy cập tài khoản quảng cáo của người dùng
        /// </summary>
        /// <param name="input">Thông tin thu hồi quyền</param>
        /// <returns>Kết quả thực hiện</returns>
        Task RevokeAdAccountsAsync(RevokeAdAccountsDto input);

        /// <summary>
        /// Lấy thống kê tổng quan về quyền truy cập
        /// </summary>
        /// <returns>Thông tin thống kê</returns>
        Task<UserAccessStatisticsDto> GetStatisticsAsync();
    }
}
