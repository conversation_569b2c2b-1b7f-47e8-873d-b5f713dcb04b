using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;

namespace TikTok.Application.Contracts.Notification
{
    /// <summary>
    /// Main service for TikTok notification operations
    /// Simplified approach without GenericFactory dependency
    /// </summary>
    public interface ITikTokNotificationService : ITransientDependency
    {
        /// <summary>
        /// Send campaign-related notification using context-based resolution
        /// </summary>
        Task<bool> SendCampaignNotificationAsync(string campaignId, string context, Dictionary<string, object> metadata = null);

        /// <summary>
        /// Send notification to specific users
        /// </summary>
        Task<bool> SendNotificationToUsersAsync(string objectId, string context, IEnumerable<TikTokNotificationUserDto> users);

        /// <summary>
        /// Get notification builder for context using simple lookup
        /// </summary>
        Task<ITikTokNotificationBuildProvider> GetNotificationBuilderAsync(string context);
    }
}
