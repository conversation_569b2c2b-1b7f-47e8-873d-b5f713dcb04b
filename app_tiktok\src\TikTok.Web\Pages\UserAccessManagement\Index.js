$(function () {
    const l = abp.localization.getResource('TikTok');
    // Use explicit REST calls because conventional controller/proxies are disabled for this app service
    const userAccessService = {
        getUsers: function (input) {
            return abp.ajax({
                url: '/api/app/user-access-management',
                type: 'GET',
                data: input,
            });
        },
        getUserAccess: function (userId) {
            return abp.ajax({
                url: `/api/app/user-access-management/${userId}`,
                type: 'GET',
            });
        },
        getAvailableAdAccounts: function (params) {
            return abp.ajax({
                url: '/api/app/user-access-management/available-ad-accounts',
                type: 'GET',
                data: params,
            });
        },
        getAssignedAdAccounts: function (userId) {
            return abp.ajax({
                url: `/api/app/user-access-management/${userId}/assigned-ad-accounts`,
                type: 'GET',
            });
        },
        getAvailableAdAccountsGroupedByBc: function (params) {
            return abp.ajax({
                url: '/api/app/user-access-management/available-ad-accounts-grouped-by-bc',
                type: 'GET',
                data: params,
            });
        },
        getAssignedAdAccountsGroupedByBc: function (params) {
            return abp.ajax({
                url: '/api/app/user-access-management/assigned-ad-accounts-grouped-by-bc',
                type: 'GET',
                data: params,
            });
        },
        assignAdAccounts: function (body) {
            return abp.ajax({
                url: '/api/app/user-access-management/assign-ad-accounts',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(body),
            });
        },
        revokeAdAccounts: function (body) {
            return abp.ajax({
                url: '/api/app/user-access-management/revoke-ad-accounts',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(body),
            });
        },
        getStatistics: function () {
            return abp.ajax({
                url: '/api/app/user-access-management/statistics',
                type: 'GET',
            });
        },
    };

    // Global variables
    let currentUserId = null;

    // BC Groups data
    let availableBcGroups = [];
    let assignedBcGroups = [];

    // Filter variables
    let currentFilters = {
        filter: '',
        roleName: '',
    };

    // Initialize DataTable with performance optimization
    const dataTable = $('#UsersTable').DataTable(
        abp.libs.datatables.normalizeConfiguration({
            serverSide: true,
            paging: true,
            pageLength: 25, // Reduce page size for better performance
            order: [[0, 'asc']],
            searching: false, // We'll use custom search
            scrollX: true,
            processing: false, // Disable DataTable processing indicator (we use custom initial loading)
            deferRender: true, // Defer rendering for better performance
            ajax: abp.libs.datatables.createAjax(function (input) {
                // Add custom filters to the input
                if (currentFilters.filter) {
                    input.filter = currentFilters.filter;
                }
                if (currentFilters.roleName) {
                    input.roleName = currentFilters.roleName;
                }
                // Removed isActive and hasAdAccountAccess filters

                return userAccessService.getUsers(input);
            }),
            columnDefs: [
                {
                    title: l('UserName'),
                    data: 'userName',
                    render: function (data, type, row) {
                        return `<strong>${data}</strong>`;
                    },
                },
                {
                    title: l('Email'),
                    data: 'email',
                },
                {
                    title: l('Name'),
                    data: null,
                    render: function (data, type, row) {
                        const fullName = [row.name, row.surname]
                            .filter((n) => n)
                            .join(' ');
                        return fullName || '-';
                    },
                },
                {
                    title: l('Role'),
                    data: 'roleName',
                    render: function (data) {
                        if (!data || data === 'No Role') {
                            return '<span class="badge bg-secondary">No Role</span>';
                        }
                        const badgeClass = getRoleBadgeClass(data);
                        return `<span class="badge ${badgeClass}">${data}</span>`;
                    },
                },
                {
                    title: l('AdAccountsCount'),
                    data: 'adAccountsCount',
                    visible: false, // Hide this column
                    render: function (data) {
                        if (data === 0) {
                            return '<span class="text-muted">0</span>';
                        }
                        return `<span class="badge bg-info">${data}</span>`;
                    },
                },
                {
                    title: l('Status'),
                    data: 'isActive',
                    render: function (data) {
                        return data
                            ? '<span class="badge bg-success">Active</span>'
                            : '<span class="badge bg-danger">Inactive</span>';
                    },
                },
                {
                    title: l('Actions'),
                    data: null,
                    orderable: false,
                    render: function (data, type, row) {
                        const actions = [];

                        // View/Manage Ad Account Access action
                        if (
                            abp.auth.isGranted(
                                'TikTok.AdAccountPermissionManagement'
                            )
                        ) {
                            const actionText = l('ManageAccess');
                            const iconClass = 'fa-cog';

                            actions.push(`
                                <button type="button" class="btn btn-sm btn-primary manage-access-btn" 
                                        data-user-id="${row.userId}" 
                                        data-user-name="${row.userName}"
                                        title="${actionText}">
                                    <i class="fas ${iconClass}"></i> ${actionText}
                                </button>
                            `);
                        }

                        return actions.join(' ');
                    },
                },
            ],
        })
    );

    // Hide initial loading row when DataTable is ready
    dataTable.on('draw', function () {
        hideLoadingState();
    });

    // Load roles for dropdown (from ABP Identity)
    function loadRoles() {
        abp.ajax({
            url: '/api/identity/roles',
            type: 'GET',
            data: { skipCount: 0, maxResultCount: 1000 },
        })
            .then(function (res) {
                const $role = $('#filterRole');
                $role.empty();
                $role.append(`<option value="">${l('AllRoles')}</option>`);
                (res.items || []).forEach(function (r) {
                    if (r && r.name) {
                        $role.append(
                            `<option value="${r.name}">${r.name}</option>`
                        );
                    }
                });
            })
            .catch(function () {
                // keep static options if identity endpoint not available
            });
    }
    loadRoles();

    // Helper function to get role badge class
    function getRoleBadgeClass(role) {
        const roleMap = {
            admin: 'bg-danger',
            supporter: 'bg-warning',
            customer: 'bg-primary',
            user: 'bg-info',
        };
        return roleMap[role.toLowerCase()] || 'bg-secondary';
    }

    // Filter functionality with loading states
    $('#applyFilters').click(function () {
        showLoadingState();
        applyFilters();
    });

    $('#clearFilters').click(function () {
        showLoadingState();
        clearFilters();
    });

    // Auto apply on typing (debounced) with loading state
    const applyFiltersDebounced = debounce(function () {
        showLoadingState();
        applyFilters();
    }, 500); // Increased debounce time for better performance
    $('#filterUser').on('input', applyFiltersDebounced);
    $('#filterRole').on('change', function () {
        showLoadingState();
        applyFilters();
    });

    // Show loading state
    function showLoadingState() {
        $('#UsersTable').addClass('table-loading');
        $('#initial-loading-row').show();
    }

    // Hide loading state
    function hideLoadingState() {
        $('#UsersTable').removeClass('table-loading');
        $('#initial-loading-row').hide();
    }

    function applyFilters() {
        currentFilters.filter = $('#filterUser').val();
        currentFilters.roleName = $('#filterRole').val();
        // Removed isActive and hasAdAccountAccess values

        dataTable.ajax.reload();
    }

    function clearFilters() {
        $('#filterUser').val('');
        $('#filterRole').val('');
        // Removed status and ad account access filters

        currentFilters = {
            filter: '',
            roleName: '',
        };

        dataTable.ajax.reload();
    }

    // Removed Refresh and Statistics features

    // Manage Access button click handler
    $(document).on('click', '.manage-access-btn', function () {
        const userId = $(this).data('user-id');
        const userName = $(this).data('user-name');
        openAdAccountAccessModal(userId, userName);
    });

    function openAdAccountAccessModal(userId, userName) {
        currentUserId = userId;
        $('#selectedUserInfo').text(`Managing access for: ${userName}`);

        // Reset pending changes
        pendingChanges = {
            toAssign: [],
            toRevoke: [],
        };

        // Show modal first for better UX
        $('#AdAccountAccessModal').modal('show');

        // Load ad accounts data after modal is shown (lazy loading)
        setTimeout(() => {
            loadAdAccountsDataGrouped(userId); // Use BC grouping approach
        }, 100);
    }

    // Debounce helper
    function debounce(fn, delay) {
        let t;
        return function () {
            const context = this,
                args = arguments;
            clearTimeout(t);
            t = setTimeout(function () {
                fn.apply(context, args);
            }, delay);
        };
    }

    // Performance monitoring
    function logPerformance(operation, startTime) {
        const endTime = performance.now();
        const duration = endTime - startTime;

        if (duration > 1000) {
            // Log slow operations (>1s)
            console.warn(
                `Slow operation: ${operation} took ${duration.toFixed(2)}ms`
            );
        }
    }

    // Initialize performance monitoring
    const pageLoadStart = performance.now();
    $(document).ready(function () {
        const pageLoadEnd = performance.now();
        logPerformance('Page Load', pageLoadStart);
    });

    // Load ad accounts data grouped by BC (new approach)
    function loadAdAccountsDataGrouped(userId) {
        // Show loading state
        $('#AvailableAdAccountsList').html(
            '<div class="text-center p-3"><i class="fas fa-spinner fa-spin"></i> Loading...</div>'
        );
        $('#AssignedAdAccountsList').html(
            '<div class="text-center p-3"><i class="fas fa-spinner fa-spin"></i> Loading...</div>'
        );

        // Load assigned ad accounts grouped by BC (direct API)
        $.ajax({
            url: '/api/app/user-access-management/assigned-ad-accounts-grouped-by-bc',
            method: 'GET',
            data: {
                userId: userId,
                filter: $('#AssignedAdAccountsSearch').val() || '',
            },
        })
            .then(function (bcGroups) {
                assignedBcGroups = bcGroups;
                renderAssignedBcGroups();

                // Load available ad accounts grouped by BC (direct API)
                return $.ajax({
                    url: '/api/app/user-access-management/available-ad-accounts-grouped-by-bc',
                    method: 'GET',
                    data: {
                        userId: userId,
                        filter: $('#AvailableAdAccountsSearch').val() || '',
                    },
                });
            })
            .then(function (bcGroups) {
                availableBcGroups = bcGroups;
                renderAvailableBcGroups();
            })
            .catch(function (error) {
                abp.notify.error(l('ErrorLoadingAdAccounts'));
                console.error(
                    'Error loading ad accounts grouped by BC:',
                    error
                );
            });
    }

    // Render available ad accounts grouped by BC
    function renderAvailableBcGroups() {
        const $container = $('#AvailableAdAccountsList');

        if (availableBcGroups.length === 0) {
            $container.html(
                '<div class="text-center p-3 text-muted">No available ad accounts found</div>'
            );
            // Update Add All button
            $('#btnAddAll').html(
                '<i class="fas fa-plus-square"></i> ' + l('AddAll') + ' (0)'
            );
            return;
        }

        // Use document fragment for better performance
        const fragment = document.createDocumentFragment();
        availableBcGroups.forEach(function (bcGroup) {
            const bcGroupHtml = createBcGroupHtml(bcGroup, 'available');
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = bcGroupHtml;
            fragment.appendChild(tempDiv.firstElementChild);
        });

        // Single DOM update
        $container.empty().append(fragment);

        // Update Add All button with total count
        const totalCount = availableBcGroups.reduce(
            (sum, group) => sum + group.totalCount,
            0
        );
        $('#btnAddAll').html(
            '<i class="fas fa-plus-square"></i> ' +
                l('AddAll') +
                ` (${totalCount})`
        );

        // Add event handlers
        addBcGroupEventHandlers('available');
    }

    // Render assigned ad accounts grouped by BC
    function renderAssignedBcGroups() {
        const $container = $('#AssignedAdAccountsList');

        if (assignedBcGroups.length === 0) {
            $container.html(
                '<div class="text-center p-3 text-muted">No assigned ad accounts found</div>'
            );
            // Update Remove All button
            $('#btnRemoveAll').html(
                '<i class="fas fa-minus-square"></i> ' + l('RemoveAll') + ' (0)'
            );
            return;
        }

        // Use document fragment for better performance
        const fragment = document.createDocumentFragment();
        assignedBcGroups.forEach(function (bcGroup) {
            const bcGroupHtml = createBcGroupHtml(bcGroup, 'assigned');
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = bcGroupHtml;
            fragment.appendChild(tempDiv.firstElementChild);
        });

        // Single DOM update
        $container.empty().append(fragment);

        // Update Remove All button with total count
        const totalCount = assignedBcGroups.reduce(
            (sum, group) => sum + group.totalCount,
            0
        );
        $('#btnRemoveAll').html(
            '<i class="fas fa-minus-square"></i> ' +
                l('RemoveAll') +
                ` (${totalCount})`
        );

        // Add event handlers
        addBcGroupEventHandlers('assigned');
    }

    // Create HTML for BC group
    function createBcGroupHtml(bcGroup, type) {
        const isExpanded = bcGroup.isExpanded ? 'expanded' : 'collapsed';
        const toggleIcon = bcGroup.isExpanded
            ? 'fa-chevron-up'
            : 'fa-chevron-down';

        // Determine button properties based on type
        const isAssigned = type === 'assigned';
        const groupButtonClass = isAssigned
            ? 'btn-outline-danger'
            : 'btn-outline-primary';
        const groupButtonIcon = isAssigned ? 'fa-minus' : 'fa-plus';
        const groupButtonTitle = isAssigned ? 'Xóa' : 'Thêm';
        const groupButtonAction = isAssigned
            ? 'bc-group-remove-all'
            : 'bc-group-add-all';

        const singleButtonClass = isAssigned
            ? 'btn-outline-danger'
            : 'btn-outline-primary';
        const singleButtonIcon = isAssigned ? 'fa-minus' : 'fa-plus';
        const singleButtonTitle = isAssigned ? 'Xóa' : 'Thêm';
        const singleButtonAction = isAssigned
            ? 'bc-group-remove-single'
            : 'bc-group-add-single';

        let html = `
            <div class="bc-group" data-bc-id="${bcGroup.bcId}" data-type="${type}">
                <div class="bc-group-header ${isExpanded}" data-bc-id="${bcGroup.bcId}">
                    <div class="bc-group-title">
                        <div class="bc-group-info">
                            <div class="bc-group-line">BC: ${bcGroup.bcName}</div>
                            <div class="bc-group-line">BCID: ${bcGroup.bcId}</div>
                        </div>
                    </div>
                    <div class="bc-group-actions">
                        <button class="btn btn-sm ${groupButtonClass} ${groupButtonAction}" data-bc-id="${bcGroup.bcId}" title="${groupButtonTitle}">
                            <i class="fas ${groupButtonIcon}"></i>
                        </button>
                        <button class="bc-group-toggle ${isExpanded}" data-bc-id="${bcGroup.bcId}">
                            <i class="fas ${toggleIcon}"></i>
                        </button>
                    </div>
                </div>
                <div class="bc-group-content ${isExpanded}">
        `;

        bcGroup.adAccounts.forEach(function (account) {
            html += `
                <div class="bc-group-account" data-advertiser-id="${
                    account.advertiserId
                }">
                    <div class="bc-group-account-info">
                        <div class="bc-group-account-line">Tài khoản: ${
                            account.name || account.advertiserId
                        }</div>
                        <div class="bc-group-account-line">ID: ${
                            account.advertiserId
                        }</div>
                        ${
                            account.company
                                ? `<div class="bc-group-account-line">Công ty: ${account.company}</div>`
                                : ''
                        }
                        </div>
                    <button class="btn btn-sm ${singleButtonClass} ${singleButtonAction}" data-advertiser-id="${
                account.advertiserId
            }" title="${singleButtonTitle}">
                        <i class="fas ${singleButtonIcon}"></i>
                    </button>
                        </div>
            `;
        });

        html += `
                    </div>
                </div>
        `;

        return html;
    }

    // Add event handlers for BC groups
    function addBcGroupEventHandlers(type) {
        const prefix =
            type === 'available'
                ? '#AvailableAdAccountsList'
                : '#AssignedAdAccountsList';

        // Toggle expand/collapse - Click anywhere on header
        $(prefix + ' .bc-group-header')
            .off('click')
            .on('click', function (e) {
                // Don't toggle if clicking on action buttons
                if ($(e.target).closest('.bc-group-actions').length > 0) {
                    return;
                }
                e.stopPropagation();
                const bcId = $(this).data('bc-id');
                toggleBcGroup(bcId, type);
            });

        // Toggle expand/collapse - Click on toggle button specifically
        $(prefix + ' .bc-group-toggle')
            .off('click')
            .on('click', function (e) {
                e.stopPropagation();
                const bcId = $(this).data('bc-id');
                toggleBcGroup(bcId, type);
            });

        // Add/Remove all accounts in BC
        $(prefix + ' .bc-group-add-all, ' + prefix + ' .bc-group-remove-all')
            .off('click')
            .on('click', function (e) {
                e.stopPropagation();
                const bcId = $(this).data('bc-id');
                addAllAccountsInBc(bcId, type);
            });

        // Add/Remove single account
        $(
            prefix +
                ' .bc-group-add-single, ' +
                prefix +
                ' .bc-group-remove-single'
        )
            .off('click')
            .on('click', function (e) {
                e.stopPropagation();
                const advertiserId = $(this).data('advertiser-id');
                addSingleAccount(advertiserId, type);
            });
    }

    // Add single account
    function addSingleAccount(advertiserId, type) {
        if (type === 'available') {
            // Move from available to assigned
            const account = availableBcGroups
                .flatMap((g) => g.adAccounts)
                .find((a) => a.advertiserId === advertiserId);

            if (account) {
                // Add to assigned
                const bcGroup = assignedBcGroups.find(
                    (g) => g.bcId === account.ownerBcId
                );
                if (bcGroup) {
                    bcGroup.adAccounts.push(account);
                    bcGroup.totalCount = bcGroup.adAccounts.length;
                } else {
                    assignedBcGroups.push({
                        bcId: account.ownerBcId,
                        bcName: `BC ${account.ownerBcId}`,
                        adAccounts: [account],
                        totalCount: 1,
                        isExpanded: false,
                    });
                }

                // Remove from available
                const availableBcGroup = availableBcGroups.find(
                    (g) => g.bcId === account.ownerBcId
                );
                if (availableBcGroup) {
                    availableBcGroup.adAccounts =
                        availableBcGroup.adAccounts.filter(
                            (a) => a.advertiserId !== advertiserId
                        );
                    availableBcGroup.totalCount =
                        availableBcGroup.adAccounts.length;

                    // Remove BC group if empty
                    if (availableBcGroup.adAccounts.length === 0) {
                        availableBcGroups = availableBcGroups.filter(
                            (g) => g.bcId !== account.ownerBcId
                        );
                    }
                }

                // Track pending change
                pendingChanges.toAssign.push(advertiserId);

                // Re-render both sides
                renderAvailableBcGroups();
                renderAssignedBcGroups();
            }
        } else if (type === 'assigned') {
            // Move from assigned to available
            const account = assignedBcGroups
                .flatMap((g) => g.adAccounts)
                .find((a) => a.advertiserId === advertiserId);

            if (account) {
                // Add to available
                const bcGroup = availableBcGroups.find(
                    (g) => g.bcId === account.ownerBcId
                );
                if (bcGroup) {
                    bcGroup.adAccounts.push(account);
                    bcGroup.totalCount = bcGroup.adAccounts.length;
                } else {
                    availableBcGroups.push({
                        bcId: account.ownerBcId,
                        bcName: `BC ${account.ownerBcId}`,
                        adAccounts: [account],
                        totalCount: 1,
                        isExpanded: false,
                    });
                }

                // Remove from assigned
                const assignedBcGroup = assignedBcGroups.find(
                    (g) => g.bcId === account.ownerBcId
                );
                if (assignedBcGroup) {
                    assignedBcGroup.adAccounts =
                        assignedBcGroup.adAccounts.filter(
                            (a) => a.advertiserId !== advertiserId
                        );
                    assignedBcGroup.totalCount =
                        assignedBcGroup.adAccounts.length;

                    // Remove BC group if empty
                    if (assignedBcGroup.adAccounts.length === 0) {
                        assignedBcGroups = assignedBcGroups.filter(
                            (g) => g.bcId !== account.ownerBcId
                        );
                    }
                }

                // Track pending change
                pendingChanges.toRevoke.push(advertiserId);

                // Re-render both sides
                renderAvailableBcGroups();
                renderAssignedBcGroups();
            }
        }
    }

    // Add all accounts in BC
    function addAllAccountsInBc(bcId, type) {
        if (type === 'available') {
            // Move all accounts from available BC to assigned
            const bcGroup = availableBcGroups.find((g) => g.bcId === bcId);
            if (bcGroup && bcGroup.adAccounts.length > 0) {
                // Add all accounts to assigned
                const assignedBcGroup = assignedBcGroups.find(
                    (g) => g.bcId === bcId
                );
                if (assignedBcGroup) {
                    assignedBcGroup.adAccounts.push(...bcGroup.adAccounts);
                    assignedBcGroup.totalCount =
                        assignedBcGroup.adAccounts.length;
                } else {
                    assignedBcGroups.push({
                        bcId: bcId,
                        bcName: bcGroup.bcName,
                        adAccounts: [...bcGroup.adAccounts],
                        totalCount: bcGroup.adAccounts.length,
                        isExpanded: false,
                    });
                }

                // Track pending changes
                bcGroup.adAccounts.forEach((account) => {
                    pendingChanges.toAssign.push(account.advertiserId);
                });

                // Remove BC group from available
                availableBcGroups = availableBcGroups.filter(
                    (g) => g.bcId !== bcId
                );

                // Re-render both sides
                renderAvailableBcGroups();
                renderAssignedBcGroups();
            }
        } else if (type === 'assigned') {
            // Move all accounts from assigned BC to available
            const bcGroup = assignedBcGroups.find((g) => g.bcId === bcId);
            if (bcGroup && bcGroup.adAccounts.length > 0) {
                // Add all accounts to available
                const availableBcGroup = availableBcGroups.find(
                    (g) => g.bcId === bcId
                );
                if (availableBcGroup) {
                    availableBcGroup.adAccounts.push(...bcGroup.adAccounts);
                    availableBcGroup.totalCount =
                        availableBcGroup.adAccounts.length;
                } else {
                    availableBcGroups.push({
                        bcId: bcId,
                        bcName: bcGroup.bcName,
                        adAccounts: [...bcGroup.adAccounts],
                        totalCount: bcGroup.adAccounts.length,
                        isExpanded: false,
                    });
                }

                // Track pending changes
                bcGroup.adAccounts.forEach((account) => {
                    pendingChanges.toRevoke.push(account.advertiserId);
                });

                // Remove BC group from assigned
                assignedBcGroups = assignedBcGroups.filter(
                    (g) => g.bcId !== bcId
                );

                // Re-render both sides
                renderAvailableBcGroups();
                renderAssignedBcGroups();
            }
        }
    }

    // Toggle BC group expand/collapse
    function toggleBcGroup(bcId, type) {
        const groups =
            type === 'available' ? availableBcGroups : assignedBcGroups;
        const group = groups.find((g) => g.bcId === bcId);

        if (group) {
            group.isExpanded = !group.isExpanded;

            // Update UI
            const $header = $(`[data-bc-id="${bcId}"].bc-group-header`);
            const $content = $(`[data-bc-id="${bcId}"] .bc-group-content`);
            const $toggle = $(`[data-bc-id="${bcId}"].bc-group-toggle`);
            const $icon = $toggle.find('i');

            if (group.isExpanded) {
                $header.removeClass('collapsed');
                $content.removeClass('collapsed');
                $toggle.addClass('expanded');
                $icon.removeClass('fa-chevron-down').addClass('fa-chevron-up');
            } else {
                $header.addClass('collapsed');
                $content.addClass('collapsed');
                $toggle.removeClass('expanded');
                $icon.removeClass('fa-chevron-up').addClass('fa-chevron-down');
            }
        }
    }

    // Show BC groups summary

    // Add all available items with BC grouping system
    $(document).on('click', '#btnAddAll', function () {
        if (!abp.auth.isGranted('TikTok.AdAccountPermissionManagement')) return;

        const $btn = $(this);

        // Count total available accounts across all BC groups
        const totalAvailableCount = availableBcGroups.reduce(
            (sum, group) => sum + group.totalCount,
            0
        );

        if (totalAvailableCount === 0) {
            abp.notify.warn('Không có tài khoản quảng cáo nào để thêm');
            return;
        }

        // Disable button and show processing state
        $btn.prop('disabled', true);
        $btn.addClass('btn-processing');
        $btn.html('<i class="fas fa-spinner fa-spin"></i> Đang xử lý...');

        // Move all available accounts to assigned
        let addedCount = 0;
        const bcIdsToProcess = [...availableBcGroups.map((g) => g.bcId)];

        bcIdsToProcess.forEach((bcId) => {
            const bcGroup = availableBcGroups.find((g) => g.bcId === bcId);
            if (bcGroup && bcGroup.adAccounts.length > 0) {
                // Track pending changes
                bcGroup.adAccounts.forEach((account) => {
                    pendingChanges.toAssign.push(account.advertiserId);
                });

                // Add all accounts to assigned
                const assignedBcGroup = assignedBcGroups.find(
                    (g) => g.bcId === bcId
                );
                if (assignedBcGroup) {
                    assignedBcGroup.adAccounts.push(...bcGroup.adAccounts);
                    assignedBcGroup.totalCount =
                        assignedBcGroup.adAccounts.length;
                } else {
                    assignedBcGroups.push({
                        bcId: bcId,
                        bcName: bcGroup.bcName,
                        adAccounts: [...bcGroup.adAccounts],
                        totalCount: bcGroup.adAccounts.length,
                        isExpanded: false,
                    });
                }
                addedCount += bcGroup.adAccounts.length;
            }
        });

        // Clear all available BC groups
        availableBcGroups = [];

        // Re-render both sides
        renderAvailableBcGroups();
        renderAssignedBcGroups();

        // Reset button
        $btn.prop('disabled', false);
        $btn.removeClass('btn-processing');
        $btn.html('<i class="fas fa-plus-square"></i> ' + l('AddAll'));
    });

    // Remove all assigned items with BC grouping system
    $(document).on('click', '#btnRemoveAll', function () {
        if (!abp.auth.isGranted('TikTok.AdAccountPermissionManagement')) return;

        const $btn = $(this);

        // Count total assigned accounts across all BC groups
        const totalAssignedCount = assignedBcGroups.reduce(
            (sum, group) => sum + group.totalCount,
            0
        );

        if (totalAssignedCount === 0) {
            abp.notify.warn('Không có tài khoản quảng cáo nào để xóa');
            return;
        }

        // Disable button and show processing state
        $btn.prop('disabled', true);
        $btn.addClass('btn-processing');
        $btn.html('<i class="fas fa-spinner fa-spin"></i> Đang xử lý...');

        // Move all assigned accounts to available
        let removedCount = 0;
        const bcIdsToProcess = [...assignedBcGroups.map((g) => g.bcId)];

        bcIdsToProcess.forEach((bcId) => {
            const bcGroup = assignedBcGroups.find((g) => g.bcId === bcId);
            if (bcGroup && bcGroup.adAccounts.length > 0) {
                // Track pending changes
                bcGroup.adAccounts.forEach((account) => {
                    pendingChanges.toRevoke.push(account.advertiserId);
                });

                // Add all accounts to available
                const availableBcGroup = availableBcGroups.find(
                    (g) => g.bcId === bcId
                );
                if (availableBcGroup) {
                    availableBcGroup.adAccounts.push(...bcGroup.adAccounts);
                    availableBcGroup.totalCount =
                        availableBcGroup.adAccounts.length;
                } else {
                    availableBcGroups.push({
                        bcId: bcId,
                        bcName: bcGroup.bcName,
                        adAccounts: [...bcGroup.adAccounts],
                        totalCount: bcGroup.adAccounts.length,
                        isExpanded: false,
                    });
                }
                removedCount += bcGroup.adAccounts.length;
            }
        });

        // Clear all assigned BC groups
        assignedBcGroups = [];

        // Re-render both sides
        renderAvailableBcGroups();
        renderAssignedBcGroups();

        // Reset button
        $btn.prop('disabled', false);
        $btn.removeClass('btn-processing');
        $btn.html('<i class="fas fa-minus-square"></i> ' + l('RemoveAll'));
    });

    function initTooltips() {
        if (
            window.bootstrap &&
            typeof window.bootstrap.Tooltip === 'function'
        ) {
            const tooltipTriggerList = Array.prototype.slice.call(
                document.querySelectorAll(
                    '#AdAccountAccessModal [data-bs-toggle="tooltip"]'
                )
            );
            tooltipTriggerList.forEach(function (tooltipTriggerEl) {
                new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }
    }

    // Save changes
    $('#SaveAdAccountAccess').click(function () {
        saveAdAccountChanges();
    });

    function saveAdAccountChanges() {
        if (
            pendingChanges.toAssign.length === 0 &&
            pendingChanges.toRevoke.length === 0
        ) {
            $('#AdAccountAccessModal').modal('hide');
            return;
        }

        // Show loading state
        const $saveBtn = $('#SaveAdAccountAccess');
        const originalText = $saveBtn.html();
        $saveBtn.prop('disabled', true);
        $saveBtn.html('<i class="fas fa-spinner fa-spin"></i> Đang lưu...');

        const promises = [];

        // Assign new ad accounts
        if (pendingChanges.toAssign.length > 0) {
            promises.push(
                userAccessService.assignAdAccounts({
                    userId: currentUserId,
                    adAccountIds: pendingChanges.toAssign,
                })
            );
        }

        // Revoke ad accounts
        if (pendingChanges.toRevoke.length > 0) {
            promises.push(
                userAccessService.revokeAdAccounts({
                    userId: currentUserId,
                    adAccountIds: pendingChanges.toRevoke,
                })
            );
        }

        Promise.all(promises)
            .then(function () {
                abp.notify.success(l('AdAccountAccessUpdated'));
                // Reset pending changes
                pendingChanges = {
                    toAssign: [],
                    toRevoke: [],
                };
                $('#AdAccountAccessModal').modal('hide');
                dataTable.ajax.reload();
            })
            .catch(function (error) {
                abp.notify.error(l('ErrorUpdatingAdAccountAccess'));
                console.error('Error updating ad account access:', error);
            })
            .finally(function () {
                // Reset button state
                $saveBtn.prop('disabled', false);
                $saveBtn.html(originalText);
            });
    }

    // Search functionality for ad accounts (BC Grouping approach)
    const searchAvailableDebounced = debounce(function () {
        // Reload BC grouped data with new search term
        loadAdAccountsDataGrouped(currentUserId);
    }, 300);
    $('#AvailableAdAccountsSearch').on('input', searchAvailableDebounced);

    // Clear search inputs
    $(document).on('click', '#ClearAvailableSearch', function () {
        $('#AvailableAdAccountsSearch').val('');
        searchAvailableDebounced();
    });

    const searchAssignedDebounced = debounce(function () {
        // Reload BC grouped data with new search term
        loadAdAccountsDataGrouped(currentUserId);
    }, 300);
    $('#AssignedAdAccountsSearch').on('input', searchAssignedDebounced);

    $(document).on('click', '#ClearAssignedSearch', function () {
        $('#AssignedAdAccountsSearch').val('');
        searchAssignedDebounced();
    });
});
