(function () {
    'use strict';

    function qs(id) {
        return document.getElementById(id);
    }

    async function onSubmit(e) {
        e.preventDefault();
        const form = qs('testForm');
        const resultArea = qs('resultArea');
        const fd = new FormData(form);
        resultArea.textContent = 'Running notification test...\n';

        try {
            const resp = await fetch('/notification-test/test', {
                method: 'POST',
                body: fd,
            });
            const data = await resp.json();
            resultArea.textContent += JSON.stringify(data, null, 2);
        } catch (err) {
            resultArea.textContent += 'Error: ' + err.message;
        }
        resultArea.scrollTop = resultArea.scrollHeight;
    }

    function clearResult() {
        const resultArea = qs('resultArea');
        if (resultArea) resultArea.textContent = '';
    }

    function init() {
        const form = qs('testForm');
        const viewUnreadLink = qs('viewUnreadLink');
        if (form) {
            form.addEventListener('submit', onSubmit);
        }

        try {
            const currentUserId =
                (window.abp && abp.currentUser && abp.currentUser.id) || '';
            if (currentUserId && viewUnreadLink) {
                viewUnreadLink.href =
                    '/api/Notifications?userId=' + currentUserId;
            }
        } catch (_) {}
    }

    window.NotificationTestMonitor = { clearResult };

    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
})();
