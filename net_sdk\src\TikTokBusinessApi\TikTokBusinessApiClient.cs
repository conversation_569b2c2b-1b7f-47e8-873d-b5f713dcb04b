/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi.Api;
using TikTokBusinessApi.Auth;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi
{
    /// <summary>
    /// Main client for TikTok Business API
    /// </summary>
    public class TikTokBusinessApiClient : IDisposable
    {
        private readonly IApiClient _apiClient;
        private bool _disposed;

        /// <summary>
        /// Ad API
        /// </summary>
        public IAdApi Ad { get; }

        /// <summary>
        /// Account API
        /// </summary>
        public IAccountApi Account { get; }

        /// <summary>
        /// Ad Account API
        /// </summary>
        public IAdAccountApi AdAccount { get; }

        /// <summary>
        /// Ad Comment API
        /// </summary>
        public IAdCommentApi AdComment { get; }

        /// <summary>
        /// Ad Diagnosis API
        /// </summary>
        public IAdDiagnosisApi AdDiagnosis { get; }

        /// <summary>
        /// Ad Group API
        /// </summary>
        public IAdGroupApi AdGroup { get; }

        /// <summary>
        /// Ad Review API
        /// </summary>
        public IAdReviewApi AdReview { get; }

        /// <summary>
        /// Audience API
        /// </summary>
        public IAudienceApi Audience { get; }

        /// <summary>
        /// Authentication API
        /// </summary>
        public IAuthenticationApi Authentication { get; }

        /// <summary>
        /// AutomatedRules API
        /// </summary>
        public IAutomatedRulesApi AutomatedRules { get; }

        /// <summary>
        /// BC Management API
        /// </summary>
        public IBcManagementApi BcManagement { get; }

        /// <summary>
        /// BC Payment API
        /// </summary>
        public IBCPaymentsApi BcPayments { get; }

        /// <summary>
        /// BC Assets API
        /// </summary>
        public IBCAssetsApi BcAssets { get; }

        // Other API properties commented out - API classes not implemented yet
        /// <summary>
        /// BC Billing Group API
        /// </summary>
        public IBcBillingGroupApi BcBillingGroup { get; }

        /// <summary>
        /// BC Reporting API
        /// </summary>
        public IBcReportingApi BcReporting { get; }

        /// <summary>
        /// Reporting API
        /// </summary>
        public IReportingApi Reporting { get; }

        /// <summary>
        /// Business Messaging API
        /// </summary>
        public IBusinessMessagingApi BusinessMessaging { get; }



        /// <summary>
        /// Campaign API
        /// </summary>
        public CampaignApi Campaign { get; }

        /// <summary>
        /// Catalog Products API
        /// </summary>
        public ICatalogProductsApi CatalogProducts { get; }

        /// <summary>
        /// Catalog Videos API
        /// </summary>
        public ICatalogVideosApi CatalogVideos { get; }

        /// <summary>
        /// Catalog Insights API
        /// </summary>
        public ICatalogInsightsApi CatalogInsights { get; }

        /// <summary>
        /// Creative Insights API
        /// </summary>
        public ICreativeInsightsApi CreativeInsights { get; }

        /// <summary>
        /// Events API
        /// </summary>
        public IEventsApi Events { get; }

        /// <summary>
        /// Events 2.0 API
        /// </summary>
        public IEvents2Api Events2 { get; }

        /// <summary>
        /// GMV Max API
        /// </summary>
        public IGMVMaxApi GMVMax { get; }

        /// <summary>
        /// Creative Portfolios API
        /// </summary>
        public ICreativePortfoliosApi CreativePortfolios { get; }

        /// <summary>
        /// Creative Reports API
        /// </summary>
        public ICreativeReportsApi CreativeReports { get; }

        /// <summary>
        /// Creative Tools API
        /// </summary>
        public ICreativeToolsApi CreativeTools { get; }

        /// <summary>
        /// Leads API
        /// </summary>
        public ILeadsApi Leads { get; }

        /// <summary>
        /// Media Mix Modeling API
        /// </summary>
        public IMediaMixModelingApi MediaMixModeling { get; }

        /// <summary>
        /// Mentions API
        /// </summary>
        public IMentionsApi Mentions { get; }

        /// <summary>
        /// Music API
        /// </summary>
        public IMusicApi Music { get; }

        /// <summary>
        /// Pangle API
        /// </summary>
        public IPangleApi Pangle { get; }

        /// <summary>
        /// Reach and Frequency API
        /// </summary>
        public IReachAndFrequencyApi ReachAndFrequency { get; }

        /// <summary>
        /// Spark Ads Recommendation API
        /// </summary>
        public ISparkAdsRecommendationApi SparkAdsRecommendation { get; }

        /// <summary>
        /// Terms API
        /// </summary>
        public ITermsApi Terms { get; }

        /// <summary>
        /// Store API
        /// </summary>
        public IStoreApi Store { get; }

        /// <summary>
        /// User API
        /// </summary>
        public IUserApi User { get; }

        /// <summary>
        /// Videos API
        /// </summary>
        public IVideosApi Videos { get; }


        /// <summary>
        /// Initializes a new instance of the TikTokBusinessApiClient class
        /// </summary>
        public TikTokBusinessApiClient() : this(Configuration.DefaultApiClient)
        {
        }

        /// <summary>
        /// Initializes a new instance of the TikTokBusinessApiClient class with an access token
        /// </summary>
        /// <param name="accessToken">Access token for authentication</param>
        public TikTokBusinessApiClient(string accessToken) : this()
        {
            // SetAccessToken(accessToken); // Commented out - method not implemented
        }

        /// <summary>
        /// Initializes a new instance of the TikTokBusinessApiClient class with a configuration
        /// </summary>
        /// <param name="configuration">Configuration instance</param>
        public TikTokBusinessApiClient(Configuration configuration) : this(configuration.CreateApiClient())
        {
            if (!string.IsNullOrEmpty(configuration.AccessToken))
            {
                // SetAccessToken(configuration.AccessToken); // Commented out - method not implemented
            }
        }

        /// <summary>
        /// Initializes a new instance of the TikTokBusinessApiClient class with a custom API client
        /// </summary>
        /// <param name="apiClient">API client instance</param>
        public TikTokBusinessApiClient(IApiClient apiClient)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));

            // Initialize API endpoints
            Ad = new AdApi(_apiClient);
            Account = new AccountApi(_apiClient);
            AdAccount = new AdAccountApi(_apiClient);
            AdComment = new AdCommentApi(_apiClient);
            AdDiagnosis = new AdDiagnosisApi(_apiClient);
            AdGroup = new AdGroupApi(_apiClient);
            AdReview = new AdReviewApi(_apiClient);
            Audience = new AudienceApi(_apiClient);
            Authentication = new AuthenticationApi(_apiClient);
            AutomatedRules = new AutomatedRulesApi(_apiClient);
            BcManagement = new BcManagementApi(_apiClient);
            BcPayments = new BCPaymentsApi(_apiClient);

            // Initialize BC Assets API
            BcAssets = new BCAssetsApi(_apiClient);

            // Initialize BC Billing Group API
            BcBillingGroup = new BcBillingGroupApi(_apiClient);

            // Initialize BC Reporting API
            BcReporting = new BcReportingApi(_apiClient);

            // Initialize Reporting API
            Reporting = new ReportingApi(_apiClient);

            // Initialize Business Messaging API
            BusinessMessaging = new BusinessMessagingApi(_apiClient);

            // Initialize Campaign API
            Campaign = new CampaignApi(_apiClient);

            // Initialize Catalog Products API
            CatalogProducts = new CatalogProductsApi(_apiClient);

            // Initialize Catalog Videos API
            CatalogVideos = new CatalogVideosApi(_apiClient);

            // Initialize Catalog Insights API
            CatalogInsights = new CatalogInsightsApi(_apiClient);

            // Initialize Creative Insights API
            CreativeInsights = new CreativeInsightsApi(_apiClient);

            // Initialize Events API
            Events = new EventsApi(_apiClient);

            // Initialize Events 2.0 API
            Events2 = new Events2Api(_apiClient);

            // Initialize GMV Max API
            GMVMax = new GMVMaxApi(_apiClient);

            // Initialize Creative Portfolios API
            CreativePortfolios = new CreativePortfoliosApi(_apiClient);

            // Initialize Creative Reports API
            CreativeReports = new CreativeReportsApi(_apiClient);

            // Initialize Creative Tools API
            CreativeTools = new CreativeToolsApi(_apiClient);

            // Initialize Leads API
            Leads = new LeadsApi(_apiClient);

            // Initialize Media Mix Modeling API
            MediaMixModeling = new MediaMixModelingApi(_apiClient);

            // Initialize Mentions API
            Mentions = new MentionsApi(_apiClient);

            // Initialize Music API
            Music = new MusicApi(_apiClient);

            // Initialize Pangle API
            Pangle = new PangleApi(_apiClient);

            // Initialize Reach and Frequency API
            ReachAndFrequency = new ReachAndFrequencyApi(_apiClient);

            // Initialize Spark Ads Recommendation API
            SparkAdsRecommendation = new SparkAdsRecommendationApi(_apiClient);

            // Initialize Terms API
            Terms = new TermsApi(_apiClient);

            // Initialize Store API
            Store = new StoreApi(_apiClient);

            // Initialize User API
            User = new UserApi(_apiClient);

            // Initialize Videos API
            Videos = new VideosApi(_apiClient);
        }

        /// <summary>
        /// Gets the underlying API client
        /// </summary>
        public IApiClient ApiClient => _apiClient;


        /// <summary>
        /// Creates a client with default configuration
        /// </summary>
        /// <returns>TikTok Business API client</returns>
        public static TikTokBusinessApiClient Create()
        {
            return new TikTokBusinessApiClient();
        }

        /// <summary>
        /// Creates a client with an access token
        /// </summary>
        /// <param name="accessToken">Access token</param>
        /// <returns>TikTok Business API client</returns>
        public static TikTokBusinessApiClient Create(string accessToken)
        {
            return new TikTokBusinessApiClient(accessToken);
        }

        /// <summary>
        /// Creates a client with a configuration
        /// </summary>
        /// <param name="configuration">Configuration</param>
        /// <returns>TikTok Business API client</returns>
        public static TikTokBusinessApiClient Create(Configuration configuration)
        {
            return new TikTokBusinessApiClient(configuration);
        }

        /// <summary>
        /// Creates a client with a logger factory
        /// </summary>
        /// <param name="loggerFactory">Logger factory</param>
        /// <returns>TikTok Business API client</returns>
        public static TikTokBusinessApiClient Create(ILoggerFactory loggerFactory)
        {
            var configuration = new Configuration
            {
                LoggerFactory = loggerFactory
            };
            return new TikTokBusinessApiClient(configuration);
        }

        /// <summary>
        /// Creates a client with an access token and logger factory
        /// </summary>
        /// <param name="accessToken">Access token</param>
        /// <param name="loggerFactory">Logger factory</param>
        /// <returns>TikTok Business API client</returns>
        public static TikTokBusinessApiClient Create(string accessToken, ILoggerFactory loggerFactory)
        {
            var configuration = new Configuration
            {
                AccessToken = accessToken,
                LoggerFactory = loggerFactory
            };
            return new TikTokBusinessApiClient(configuration);
        }

        /// <summary>
        /// Disposes the client and releases resources
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                _apiClient?.Dispose();
                _disposed = true;
            }
        }
    }
}
