using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TikTok.AdAccounts;
using TikTok.Dapper;
using TikTok.Entities;

namespace TikTok.Repositories
{
    /// <summary>
    /// Dapper repository cho AdAccount để bypass EF Core limitations
    /// </summary>
    public interface IAdAccountDapperRepository : IDapperRepository<RawAdAccountEntity>
    {
        /// <summary>
        /// Lấy tất cả ad accounts với SQL query (không bị giới hạn 1000)
        /// </summary>
        /// <param name="filter">Filter text</param>
        /// <param name="bcId">Business Center ID</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <returns>Danh sách ad accounts entities</returns>
        Task<List<RawAdAccountEntity>> GetAllAdAccountsAsync(string? filter = null, string? bcId = null, string? advertiserId = null);

        /// <summary>
        /// Lấy ad accounts theo advertiser IDs (batch query)
        /// </summary>
        /// <param name="advertiserIds">Danh sách advertiser IDs</param>
        /// <returns>Danh sách ad accounts entities</returns>
        Task<List<RawAdAccountEntity>> GetAdAccountsByAdvertiserIdsAsync(List<string> advertiserIds);
    }
}
