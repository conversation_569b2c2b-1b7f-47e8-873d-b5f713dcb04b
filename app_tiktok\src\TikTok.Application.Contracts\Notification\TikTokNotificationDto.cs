using System.Collections.Generic;
using TikTok.Enums;

namespace TikTok.Application.Contracts.Notification
{
    /// <summary>
    /// TikTok-specific notification DTO
    /// Extends the pattern from Meeting Management
    /// </summary>
    public class TikTokNotificationDto
    {
        public string UserId { get; set; }
        public TikTokUserType UserType { get; set; }
        public string ObjectId { get; set; }
        public string Context { get; set; }
        public string Title { get; set; }
        public string Content { get; set; }
        public string Payload { get; set; } // JSON payload for additional data
        public Dictionary<string, object> Metadata { get; set; } = new();
        public string PhoneNumber { get; set; }
        public string AdAccountId { get; set; }
        public string BcId { get; set; }
    }

    /// <summary>
    /// User information for notification targeting
    /// </summary>
    public class TikTokNotificationUserDto
    {
        public string UserId { get; set; }
        public TikTokUserType UserType { get; set; }
        public string PhoneNumber { get; set; }
        public string AdAccountId { get; set; }
        public string BcId { get; set; }
    }

    /// <summary>
    /// Standard NotificationDto from Tsp.Module.Notifications
    /// Used by context providers
    /// </summary>
    public class NotificationDto
    {
        public string ObjectId { get; set; }
        public string Context { get; set; }
        public string Payload { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new();
    }
}
