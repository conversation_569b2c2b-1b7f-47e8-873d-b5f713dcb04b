using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Application.Contracts.Notification;
using TikTok.Consts;
using TikTok.Entities;
using TikTok.Enums;
using TikTok.Repositories;
using TikTok.ResourcePermissions;
using TikTok.Users;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Identity;
using Newtonsoft.Json;

namespace TikTok.Application.Notification.Builders
{
    /// <summary>
    /// Notification builder for GMV Max Creative Status Change
    /// Triggers when creative delivery status changes to disabled states
    /// </summary>
    public class GmvMaxCreativeStatusChangeNotificationBuilder : TikTokNotificationBuildBase
    {
        private readonly IRawGmvMaxProductCreativeReportRepository _creativeReportRepository;
        private readonly IRawGmvMaxCampaignsRepository _campaignRepository;
    private readonly IResourcePermissionAppService _resourcePermissionAppService;
    private readonly IResourcePermissionRepository _resourcePermissionRepository;
    private readonly IIdentityUserRepository _identityUserRepository;
    private readonly IdentityUserManager _userManager;
    private readonly IUserManagementService _userManagementService;

        public GmvMaxCreativeStatusChangeNotificationBuilder(
            IAbpLazyServiceProvider abpLazyServiceProvider,
            IRawGmvMaxProductCreativeReportRepository creativeReportRepository,
            IRawGmvMaxCampaignsRepository campaignRepository,
            IResourcePermissionAppService resourcePermissionAppService,
            IResourcePermissionRepository resourcePermissionRepository,
            IIdentityUserRepository identityUserRepository,
            IdentityUserManager userManager,
            IUserManagementService userManagementService)
            : base(abpLazyServiceProvider)
        {
            _creativeReportRepository = creativeReportRepository;
            _campaignRepository = campaignRepository;
            _resourcePermissionAppService = resourcePermissionAppService;
            _resourcePermissionRepository = resourcePermissionRepository;
            _identityUserRepository = identityUserRepository;
            _userManager = userManager;
            _userManagementService = userManagementService;
        }

        /// <summary>
        /// Context identifier for this notification builder
        /// </summary>
        public override string Context => TikTokNotificationConst.GmvMaxCreativeStatusChange;

        /// <summary>
        /// User types allowed to receive GMV Max creative status notifications
        /// </summary>
        public override IEnumerable<TikTokUserType> AllowSendToUserTypes =>
            new List<TikTokUserType>
            {
                TikTokUserType.CAMPAIGN_MANAGER,
                TikTokUserType.CREATIVE_APPROVER,
                TikTokUserType.ADVERTISER,
                TikTokUserType.ACCOUNT_MANAGER
            };

        /// <summary>
        /// Get notification template for specific user type
        /// </summary>
        public override Task<string> GetTemplateTitle(TikTokUserType userType, TikTokEntityType? entityType = null)
        {
            string template = userType switch
            {
                TikTokUserType.CAMPAIGN_MANAGER => TikTokNotificationTemplateConst.GmvMaxCreativeStatusChange.CAMPAIGN_MANAGER,
                TikTokUserType.CREATIVE_APPROVER => TikTokNotificationTemplateConst.GmvMaxCreativeStatusChange.CREATIVE_APPROVER,
                TikTokUserType.ADVERTISER => TikTokNotificationTemplateConst.GmvMaxCreativeStatusChange.ADVERTISER,
                TikTokUserType.ACCOUNT_MANAGER => TikTokNotificationTemplateConst.GmvMaxCreativeStatusChange.ACCOUNT_MANAGER,
                _ => TikTokNotificationTemplateConst.GmvMaxCreativeStatusChange.ADVERTISER
            };

            return Task.FromResult(template);
        }

        /// <summary>
        /// Build short dynamic title: "[GMV Max] {CampaignName}: {Total} video lỗi"
        /// Role-specific prefix can be simplified; detailed breakdown stays in content.
        /// </summary>
        protected override Task<string> BuildNotificationTitle(object entityData, TikTokUserType userType)
        {
            var statusSummary = entityData as CampaignStatusSummary;
            if (statusSummary == null)
            {
                // Fallback to role template if no data
                return GetTemplateTitle(userType);
            }

            var total = statusSummary.ProblematicCreativesCount;
            var campaignName = string.IsNullOrWhiteSpace(statusSummary.CampaignName) ? "Campaign" : statusSummary.CampaignName;

            // Keep it short; Vietnamese phrasing concise
            // Examples:
            // [GMV Max] ABC: 3 video lỗi
            // [GMV Max] ABC: 1 video lỗi
            var title = $"[GMV Max] {campaignName}: {total} video lỗi";
            return Task.FromResult(title);
        }

        /// <summary>
        /// Get campaign data for notification building
        /// </summary>
        protected override async Task<object> GetEntityData(string campaignId)
        {
            try
            {
                Logger.LogInformation("NOTIFICATION BUILDER: Received CampaignId: {CampaignId}", campaignId);
                
                // Get campaign information
                var campaign = await _campaignRepository.FindAsync(c => c.CampaignId == campaignId);
                if (campaign == null)
                {
                    Logger.LogWarning("NOTIFICATION BUILDER: Campaign not found: {CampaignId}", campaignId);
                    return null;
                }
                
                Logger.LogInformation("NOTIFICATION BUILDER: Found campaign: {CampaignId} - {CampaignName}", 
                    campaign.CampaignId, campaign.CampaignName);

                // Get creative reports for this campaign
                var creativeReports = await _creativeReportRepository.GetListAsync(
                    c => c.CampaignId == campaignId);

                // Create status summary
                var statusSummary = new CampaignStatusSummary
                {
                    CampaignId = campaignId,
                    CampaignName = campaign.CampaignName,
                    AdvertiserId = campaign.AdvertiserId,
                    BcId = campaign.BcId,
                    TotalCreatives = creativeReports.Count,
                    LastChangeTime = DateTime.UtcNow
                };

                // Count creatives by status
                foreach (var report in creativeReports)
                {
                    statusSummary.StatusCounts[report.CreativeDeliveryStatus] = 
                        statusSummary.StatusCounts.GetValueOrDefault(report.CreativeDeliveryStatus, 0) + 1;
                }

                // Guard: do not send notifications when there are no problematic creatives
                if (statusSummary.ProblematicCreativesCount <= 0)
                {
                    Logger.LogInformation("NOTIFICATION BUILDER: Skip sending - no problematic creatives for campaign {CampaignId}", campaignId);
                    return null;
                }

                return statusSummary;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error getting campaign data for {CampaignId}", campaignId);
                return null;
            }
        }

        /// <summary>
        /// Get users who should receive notifications for this campaign
        /// RULE: Chỉ gửi thông báo cho admin users và users có quyền truy cập AdAccount của campaign
        /// </summary>
        protected override async Task<IEnumerable<TikTokNotificationUserDto>> GetNotificationUsers(object entityData)
        {
            var statusSummary = entityData as CampaignStatusSummary;
            if (statusSummary == null)
            {
                Logger.LogWarning("StatusSummary is null, no users to notify");
                return Enumerable.Empty<TikTokNotificationUserDto>();
            }

            var users = new List<TikTokNotificationUserDto>();

            try
            {
                Logger.LogInformation("Getting notification users for campaign {CampaignId} with AdvertiserId {AdvertiserId}", 
                    statusSummary.CampaignId, statusSummary.AdvertiserId);

                // 1. Lấy tất cả users có quyền truy cập AdAccount của campaign này
                // Sử dụng repository trực tiếp
                var allUserPermissions = await _resourcePermissionRepository.GetListAsync(
                    x => x.ResourceType == "AdAccount" && x.ResourceId == statusSummary.AdvertiserId
                );

                var authorizedUserIds = allUserPermissions
                    .Select(p => p.UserId)
                    .Distinct()
                    .ToList();

                Logger.LogInformation("Found {Count} users with AdAccount permissions for AdvertiserId {AdvertiserId}", 
                    authorizedUserIds.Count, statusSummary.AdvertiserId);

                // 2. Lấy tất cả admin users - OPTIMIZED để tránh N+1 query
                var allUsers = await _userManagementService.GetAllUsersAsync();
                var adminUsers = new List<UserDto>();

                // Batch load tất cả IdentityUsers để tránh N+1 query
                var userIds = allUsers.Select(u => u.Id).ToList();
                var identityUsers = await _identityUserRepository.GetListAsync();
                var identityUserDict = identityUsers.Where(u => userIds.Contains(u.Id)).ToDictionary(u => u.Id, u => u);

                Logger.LogInformation("Loaded {Count} identity users for role checking", identityUsers.Count);

                // Batch process roles để tránh N+1 query
                foreach (var user in allUsers)
                {
                    try
                    {
                        if (identityUserDict.TryGetValue(user.Id, out var identityUser))
                        {
                            var roles = await _userManager.GetRolesAsync(identityUser);
                            if (roles.Any(r => r.Contains("Admin", StringComparison.OrdinalIgnoreCase) || 
                                              r.Contains("Administrator", StringComparison.OrdinalIgnoreCase)))
                            {
                                adminUsers.Add(user);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Logger.LogWarning(ex, "Could not get roles for user {UserId}", user.Id);
                    }
                }

                Logger.LogInformation("Found {Count} admin users", adminUsers.Count);

                // 3. Combine authorized users and admin users
                var allTargetUserIds = authorizedUserIds.Union(adminUsers.Select(u => u.Id)).Distinct().ToList();

                // 4. Tạo TikTokNotificationUserDto cho từng user - OPTIMIZED
                // Sử dụng dictionary đã load trước đó để tránh N+1 query
                foreach (var userId in allTargetUserIds)
                {
                    try
                    {
                        // Lấy IdentityUser từ dictionary đã load trước đó
                        if (!identityUserDict.TryGetValue(userId, out var identityUser) || identityUser == null)
                        {
                            Logger.LogWarning("IdentityUser {UserId} not found, skipping", userId);
                            continue;
                        }

                        // Determine user type based on roles
                        var roles = await _userManager.GetRolesAsync(identityUser);
                        var userType = DetermineUserType(roles);

                        users.Add(new TikTokNotificationUserDto
                        {
                            UserId = userId.ToString(),
                            UserType = userType,
                            AdAccountId = statusSummary.AdvertiserId,
                            BcId = statusSummary.BcId,
                            PhoneNumber = identityUser.PhoneNumber
                        });

                        Logger.LogDebug("Added notification user: {UserId} ({UserType}) for campaign {CampaignId}", 
                            userId, userType, statusSummary.CampaignId);
                    }
                    catch (Exception ex)
                    {
                        Logger.LogWarning(ex, "Could not process user {UserId} for notifications", userId);
                    }
                }

                Logger.LogInformation("Total {Count} users will receive notifications for campaign {CampaignId}", 
                    users.Count, statusSummary.CampaignId);

                return users;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error getting notification users for campaign {CampaignId}", statusSummary.CampaignId);
                return Enumerable.Empty<TikTokNotificationUserDto>();
            }
        }

        /// <summary>
        /// Determine user type based on roles
        /// </summary>
        private TikTokUserType DetermineUserType(IList<string> roles)
        {
            if (roles.Any(r => r.Contains("Admin", StringComparison.OrdinalIgnoreCase) || 
                              r.Contains("Administrator", StringComparison.OrdinalIgnoreCase)))
            {
                return TikTokUserType.ACCOUNT_MANAGER;
            }
            else if (roles.Any(r => r.Contains("Manager", StringComparison.OrdinalIgnoreCase)))
            {
                return TikTokUserType.CAMPAIGN_MANAGER;
            }
            else if (roles.Any(r => r.Contains("Approver", StringComparison.OrdinalIgnoreCase)))
            {
                return TikTokUserType.CREATIVE_APPROVER;
            }
            else
            {
                return TikTokUserType.ADVERTISER;
            }
        }

        /// <summary>
        /// Build notification content for specific user type
        /// </summary>
        protected override Task<string> BuildNotificationContent(object entityData, TikTokUserType userType)
        {
            var statusSummary = entityData as CampaignStatusSummary;
            if (statusSummary == null)
            {
                return Task.FromResult("Campaign data not available");
            }

            // Get template for user type
            var template = userType switch
            {
                TikTokUserType.CAMPAIGN_MANAGER => TikTokNotificationTemplateConst.GmvMaxCreativeStatusChange.CAMPAIGN_MANAGER,
                TikTokUserType.CREATIVE_APPROVER => TikTokNotificationTemplateConst.GmvMaxCreativeStatusChange.CREATIVE_APPROVER,
                TikTokUserType.ADVERTISER => TikTokNotificationTemplateConst.GmvMaxCreativeStatusChange.ADVERTISER,
                TikTokUserType.ACCOUNT_MANAGER => TikTokNotificationTemplateConst.GmvMaxCreativeStatusChange.ACCOUNT_MANAGER,
                _ => TikTokNotificationTemplateConst.GmvMaxCreativeStatusChange.ADVERTISER
            };

            // Replace placeholders with actual values
            var replacements = new Dictionary<string, string>
            {
                { TikTokNotificationTemplateConst.CampaignName, statusSummary.CampaignName },
                { TikTokNotificationTemplateConst.TotalProblematicCount, statusSummary.ProblematicCreativesCount.ToString() },
                { TikTokNotificationTemplateConst.NotDeliveryCount, statusSummary.GetStatusCount(CreativeDeliveryStatus.NOT_DELIVERYIN).ToString() },
                { TikTokNotificationTemplateConst.ExcludedCount, statusSummary.GetStatusCount(CreativeDeliveryStatus.EXCLUDED).ToString() },
                { TikTokNotificationTemplateConst.UnavailableCount, statusSummary.GetStatusCount(CreativeDeliveryStatus.UNAVAILABLE).ToString() },
                { TikTokNotificationTemplateConst.RejectedCount, statusSummary.GetStatusCount(CreativeDeliveryStatus.REJECTED).ToString() }
            };

            var content = ReplaceTemplatePlaceholders(template, replacements);
            return Task.FromResult(content);
        }

        /// <summary>
        /// Build notification payload with detailed information
        /// </summary>
        protected override Task<string> BuildNotificationPayload(object entityData, TikTokUserType userType)
        {
            var statusSummary = entityData as CampaignStatusSummary;
            if (statusSummary == null)
            {
                return Task.FromResult("{}");
            }

            var payload = new
            {
                UserType = userType,
                CampaignId = statusSummary.CampaignId,
                CampaignName = statusSummary.CampaignName,
                AdvertiserId = statusSummary.AdvertiserId,
                BcId = statusSummary.BcId,
                TotalCreatives = statusSummary.TotalCreatives,
                ProblematicCreativesCount = statusSummary.ProblematicCreativesCount,
                StatusCounts = new
                {
                    NotDelivery = statusSummary.GetStatusCount(CreativeDeliveryStatus.NOT_DELIVERYIN),
                    Excluded = statusSummary.GetStatusCount(CreativeDeliveryStatus.EXCLUDED),
                    Unavailable = statusSummary.GetStatusCount(CreativeDeliveryStatus.UNAVAILABLE),
                    Rejected = statusSummary.GetStatusCount(CreativeDeliveryStatus.REJECTED)
                },
                ChangedCreativeIds = statusSummary.ChangedCreativeIds,
                LastChangeTime = statusSummary.LastChangeTime,
                Timestamp = DateTime.UtcNow
            };

            return Task.FromResult(JsonConvert.SerializeObject(payload));
        }

        /// <summary>
        /// Get notification context for this builder
        /// </summary>
        protected override string GetNotificationContext()
        {
            return TikTokNotificationConst.GmvMaxCreativeStatusChange;
        }

        /// <summary>
        /// Check if creative delivery status indicates disabled state
        /// </summary>
        private bool IsCreativeDisabled(CreativeDeliveryStatus? status)
        {
            if (!status.HasValue) return false;

            return status.Value == CreativeDeliveryStatus.NOT_DELIVERYIN ||
                   status.Value == CreativeDeliveryStatus.EXCLUDED ||
                   status.Value == CreativeDeliveryStatus.UNAVAILABLE ||
                   status.Value == CreativeDeliveryStatus.REJECTED;
        }
    }

}
